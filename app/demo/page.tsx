"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function DemoPage() {
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [showExplanation, setShowExplanation] = useState(false)
  const [isCorrect, setIsCorrect] = useState(false)

  const demoQuestion = {
    text: "What energy transformation occurs when you turn on an electric fan?",
    options: ["Chemical to mechanical", "Electrical to mechanical", "Heat to mechanical", "Light to mechanical"],
    correctAnswer: "Electrical to mechanical",
    explanation:
      "When you turn on an electric fan, electrical energy from the power source flows through the fan's motor, which converts it into mechanical energy that rotates the blades. This is an example of electrical energy transforming into mechanical energy.",
  }

  const handleAnswerSelect = (answer: string) => {
    setSelectedAnswer(answer)
  }

  const handleCheckAnswer = () => {
    if (selectedAnswer === null) return

    const correct = selectedAnswer === demoQuestion.correctAnswer
    setIsCorrect(correct)
    setShowExplanation(true)
  }

  const handleReset = () => {
    setSelectedAnswer(null)
    setShowExplanation(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-yellow-100 to-orange-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-yellow-200 shadow-xl">
          <CardHeader className="bg-yellow-50 border-b border-yellow-200">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl text-yellow-700">Demo Question</CardTitle>
              <div className="text-sm text-yellow-600 font-medium">Energy Transformation</div>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-4">{demoQuestion.text}</h3>

              <RadioGroup value={selectedAnswer || ""} className="space-y-3" disabled={showExplanation}>
                {demoQuestion.options.map((option, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-2 border-2 rounded-lg p-3 transition-colors ${
                      showExplanation
                        ? option === demoQuestion.correctAnswer
                          ? "border-green-500 bg-green-50"
                          : option === selectedAnswer
                            ? "border-red-500 bg-red-50"
                            : "border-gray-200"
                        : "border-gray-200 hover:border-yellow-300"
                    }`}
                  >
                    <RadioGroupItem
                      value={option}
                      id={`demo-option-${index}`}
                      onClick={() => handleAnswerSelect(option)}
                      className={
                        showExplanation
                          ? option === demoQuestion.correctAnswer
                            ? "text-green-600"
                            : "text-red-600"
                          : "text-yellow-600"
                      }
                      disabled={showExplanation}
                    />
                    <Label htmlFor={`demo-option-${index}`} className="flex-grow cursor-pointer py-1">
                      {option}
                    </Label>
                  </div>
                ))}
              </RadioGroup>

              {showExplanation && (
                <Alert
                  className={`mt-6 ${isCorrect ? "bg-green-50 border-green-200" : "bg-amber-50 border-amber-200"}`}
                >
                  <AlertTitle className={isCorrect ? "text-green-700" : "text-amber-700"}>
                    {isCorrect ? "Correct!" : "Not quite right"}
                  </AlertTitle>
                  <AlertDescription className="mt-2">{demoQuestion.explanation}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>

          <CardFooter className="bg-gray-50 border-t border-gray-200 p-4">
            {!showExplanation ? (
              <Button
                onClick={handleCheckAnswer}
                disabled={selectedAnswer === null}
                className="ml-auto bg-yellow-500 hover:bg-yellow-600"
              >
                Check Answer
              </Button>
            ) : (
              <div className="flex gap-4 ml-auto">
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="border-2 border-yellow-300 text-yellow-600 hover:bg-yellow-50"
                >
                  Try Again
                </Button>
                <Link href="/">
                  <Button className="bg-yellow-500 hover:bg-yellow-600">Back to Home</Button>
                </Link>
              </div>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
