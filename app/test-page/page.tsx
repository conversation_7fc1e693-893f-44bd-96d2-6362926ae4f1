"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestPage() {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-100 to-blue-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-green-200 shadow-xl">
          <CardHeader className="bg-green-50 border-b border-green-200">
            <CardTitle className="text-2xl text-green-700">Test Page</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <p>This is a test page to check if the syntax error is route-specific</p>
            <Button className="bg-green-500 hover:bg-green-600">
              Test Button
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
