"use client"

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export default function TestSupabasePage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [password, setPassword] = useState('password123')
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testConnection = async () => {
    setIsLoading(true)
    setTestResults([])
    
    try {
      addResult('🔍 Testing Supabase connection...')
      
      // Test 1: Basic connection
      addResult('1. Testing basic connection to Supabase...')
      const { data: healthCheck, error: healthError } = await supabase
        .from('teachers')
        .select('count')
        .limit(1)
      
      if (healthError) {
        addResult(`❌ Connection failed: ${healthError.message}`)
      } else {
        addResult('✅ Basic connection successful')
      }

      // Test 2: Authentication test
      addResult('2. Testing authentication...')
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        addResult(`❌ Auth failed: ${authError.message}`)
        
        // Provide specific solutions
        if (authError.message.includes('Invalid login credentials')) {
          addResult('💡 Solution: Email/password incorrect or user doesn\'t exist')
        } else if (authError.message.includes('Email not confirmed')) {
          addResult('💡 Solution: Confirm email or disable email confirmation in Supabase')
        } else if (authError.message.includes('User not found')) {
          addResult('💡 Solution: Create teacher account first')
        }
      } else {
        addResult('✅ Authentication successful!')
        addResult(`👤 User ID: ${authData.user?.id}`)
        addResult(`📧 Email: ${authData.user?.email}`)
        
        // Sign out after test
        await supabase.auth.signOut()
        addResult('🚪 Signed out after test')
      }

      // Test 3: Check teachers table
      addResult('3. Checking teachers table...')
      const { data: teachers, error: teachersError } = await supabase
        .from('teachers')
        .select('*')
        .limit(5)

      if (teachersError) {
        addResult(`❌ Teachers table error: ${teachersError.message}`)
      } else {
        addResult(`✅ Teachers table accessible - Found ${teachers.length} teachers`)
        teachers.forEach((teacher, index) => {
          addResult(`   ${index + 1}. ${teacher.email} (Auth ID: ${teacher.auth_id})`)
        })
      }

    } catch (error: any) {
      addResult(`❌ Unexpected error: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const createTestTeacher = async () => {
    setIsLoading(true)
    addResult('🔧 Creating test teacher account...')
    
    try {
      // This would need to be done via API route or server action
      // For now, just show instructions
      addResult('⚠️ Teacher creation requires server-side operation')
      addResult('📝 Manual steps:')
      addResult('1. Go to Supabase Dashboard > Authentication > Users')
      addResult('2. Click "Add User"')
      addResult(`3. Email: ${email}`)
      addResult(`4. Password: ${password}`)
      addResult('5. Go to Table Editor > teachers')
      addResult('6. Insert row with auth_id from step 4')
      
    } catch (error: any) {
      addResult(`❌ Error: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>🧪 Supabase Connection Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Test Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="password">Test Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={testConnection} 
                disabled={isLoading}
                className="bg-blue-500 hover:bg-blue-600"
              >
                {isLoading ? 'Testing...' : 'Test Connection & Auth'}
              </Button>
              <Button 
                onClick={createTestTeacher} 
                disabled={isLoading}
                variant="outline"
              >
                Show Teacher Creation Steps
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>📋 Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-gray-500">Click "Test Connection & Auth" to start testing...</div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>🔧 Environment Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}</div>
              <div>Supabase Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}</div>
              <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Server-side'}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
