"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Loader2 } from "lucide-react"
import { getStudentTestResults } from "@/lib/api"
import { toast } from "sonner"

export default function ResultsPage() {
  const [score, setScore] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(20)
  const [level, setLevel] = useState<string>("")
  const [newLevel, setNewLevel] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchResults = async () => {
      try {
        // Get basic info from localStorage first
        const testScore = localStorage.getItem("testScore") || "0"
        const studentLevel = localStorage.getItem("studentLevel") || "easy"
        const prevLevel = localStorage.getItem("prevLevel") || studentLevel
        const studentId = localStorage.getItem("studentId")

        setScore(Number.parseInt(testScore))
        setLevel(prevLevel)
        setNewLevel(studentLevel)

        // If we have a student ID, fetch the latest test result from the database
        if (studentId) {
          const testResults = await getStudentTestResults(studentId)

          if (testResults && testResults.length > 0) {
            // Get the most recent test result (first in the array)
            const latestResult = testResults[0]

            setScore(latestResult.score)
            setTotalQuestions(latestResult.total_questions)

            if (latestResult.previous_level) {
              setLevel(latestResult.previous_level)
            }

            setNewLevel(latestResult.new_level)
          }
        }
      } catch (error) {
        console.error("Error fetching test results:", error)
        toast.error("Failed to load test results")
      } finally {
        setIsLoading(false)
      }
    }

    fetchResults()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-green-100 to-yellow-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center p-6">
          <h2 className="text-xl font-bold mb-4">Loading results...</h2>
          <Loader2 className="h-12 w-12 animate-spin text-green-500 mx-auto" />
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-100 to-yellow-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-green-300 shadow-xl">
          <CardHeader className="bg-green-50 border-b border-green-200 text-center">
            <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            <CardTitle className="text-3xl text-green-700">Congratulations!</CardTitle>
          </CardHeader>

          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">You've Successfully Completed the Assessment</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white p-4 rounded-lg shadow border border-green-200">
                <div className="text-4xl font-bold text-green-600 mb-2">{score}</div>
                <div className="text-gray-600">Questions Correct</div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border border-green-200">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {Math.round((score / totalQuestions) * 100)}%
                </div>
                <div className="text-gray-600">Score Percentage</div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border border-green-200">
                <div className="text-4xl font-bold text-green-600 mb-2">
                  {newLevel.charAt(0).toUpperCase() + newLevel.slice(1)}
                </div>
                <div className="text-gray-600">Achievement Level</div>
              </div>
            </div>

            {level !== newLevel && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
                <p className="text-yellow-700">
                  You've been upgraded from{" "}
                  <span className="font-bold">{level.charAt(0).toUpperCase() + level.slice(1)}</span> to{" "}
                  <span className="font-bold">{newLevel.charAt(0).toUpperCase() + newLevel.slice(1)}</span> level!
                </p>
              </div>
            )}

            <p className="text-gray-600 mb-8">
              Great job on completing your Energy Transformation assessment! You've demonstrated a good understanding of
              how energy changes from one form to another.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/student/dashboard">
                <Button className="bg-green-500 hover:bg-green-600 px-6">Go to Dashboard</Button>
              </Link>

              <Link href="/">
                <Button variant="outline" className="border-2 border-green-300 text-green-600 hover:bg-green-50 px-6">
                  Back to Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
