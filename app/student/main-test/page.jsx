"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getMainTestQuestions, saveTestResult, updateStudentLevel } from "@/lib/api";
import { Home, Loader2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export default function MainTestPage() {
  const [studentLevel, setStudentLevel] = useState("easy");
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [score, setScore] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [kkm, setKkm] = useState(70); // Minimum passing score percentage
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);


  const [isBrowser, setIsBrowser] = useState(false); // Add state to track if we're in browser environment
  const [loadingTime, setLoadingTime] = useState(0); // Track loading time
  const router = useRouter();

  // Effect to set isBrowser state when component mounts in browser
  useEffect(() => {
    setIsBrowser(true);
  }, []);

  // Main effect to fetch data after we know we're in the browser
  useEffect(() => {
    // Only run this effect in the browser
    if (!isBrowser) return;

    const fetchData = async () => {
      try {
        // Get student level from localStorage if available
        let level = "easy";
        if (isLocalStorageAvailable()) {
          level = window.localStorage.getItem("studentLevel") || "easy";
        }
        setStudentLevel(level);

        // Use default settings instead of fetching from API
        const defaultSettings = {
          kkm: 70,
          enable_ai: false, // Set to false to always use question bank
          main_test_questions: 20
        };

        setKkm(defaultSettings.kkm);

        console.log("Using default settings:", defaultSettings);

        // Always get questions from the question bank
        setIsLoading(true);
        console.log(`Getting ${defaultSettings.main_test_questions} questions from question bank with level: ${level}`);
        const testQuestions = await getMainTestQuestions(level, defaultSettings.main_test_questions);

        if (testQuestions && testQuestions.length > 0) {
          console.log(`Successfully loaded ${testQuestions.length} questions from question bank`);
          setQuestions(testQuestions);
          toast.success(`Berhasil memuat ${testQuestions.length} pertanyaan`);
        } else {
          console.warn("No questions found in question bank, using mock questions");
          useMockQuestions(level, defaultSettings.main_test_questions);
          toast.info("Menggunakan pertanyaan cadangan karena tidak ada pertanyaan tersedia");
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching test data:", error);
        toast.error("Gagal memuat pertanyaan tes");
        // Use mock questions as fallback
        useMockQuestions(level, 20);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isBrowser]);

  // Helper function to check if localStorage is available
  const isLocalStorageAvailable = () => {
    // First check if we're in the browser
    if (!isBrowser || typeof window === 'undefined') {
      return false;
    }

    try {
      // Test if localStorage is available
      const testKey = '__test__';
      window.localStorage.setItem(testKey, testKey);
      window.localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      console.log('localStorage test failed:', e);
      return false;
    }
  };

  // Helper function to use mock questions
  const useMockQuestions = async (level, count) => {
    try {
      const { getMockQuestionResponse } = await import('@/lib/gemini-ai');
      const mockQuestions = [];

      for (let i = 0; i < count; i++) {
        mockQuestions.push({
          ...getMockQuestionResponse(level),
          id: `mock-question-${i + 1}`
        });
      }

      setQuestions(mockQuestions);
      console.log(`Using ${mockQuestions.length} mock questions`);
    } catch (error) {
      console.error("Error creating mock questions:", error);

      // Last resort: empty array with error message
      setQuestions([]);
      toast.error("Gagal memuat pertanyaan. Silakan muat ulang halaman.");
    }
  };

  const currentQuestion = questions[currentQuestionIndex];
  const progress = questions.length ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0;

  const handleAnswerSelect = (answer) => {
    setSelectedAnswer(answer);
  };

  // Simplified handleCheckAnswer function
  const handleCheckAnswer = () => {
    if (selectedAnswer === null || !currentQuestion) return;

    const correct = selectedAnswer === currentQuestion.correct_answer;
    setIsCorrect(correct);

    if (correct) {
      setScore(score + 1);
    }

    // Show explanation from the question bank
    setShowExplanation(true);
  };

  const handleNextQuestion = () => {
    setSelectedAnswer(null);
    setShowExplanation(false);

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setShowResult(true);
    }
  };

  const handleFinishTest = async () => {
    try {
      setIsSaving(true);

      // Check if localStorage is available
      if (!isLocalStorageAvailable()) {
        toast.error("Penyimpanan lokal tidak tersedia. Silakan aktifkan cookies di browser Anda.");
        return;
      }

      const studentId = window.localStorage.getItem("studentId");
      if (!studentId) {
        toast.error("ID Siswa tidak ditemukan. Silakan login kembali.");
        router.push("/student/login");
        return;
      }

      const scorePercentage = (score / questions.length) * 100;
      const passed = scorePercentage >= kkm;

      // Determine new level
      let newLevel = studentLevel;
      if (passed && studentLevel !== "hard") {
        newLevel = studentLevel === "easy" ? "medium" : "hard";
      }

      // Save test result to database
      await saveTestResult({
        student_id: studentId,
        test_type: 'main-test',
        score,
        total_questions: questions.length,
        passed,
        previous_level: studentLevel,
        new_level: newLevel,
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        subject: "sains",
        topic: "sains"
      });

      // Update student level if passed
      if (passed && studentLevel !== newLevel) {
        await updateStudentLevel(studentId, newLevel);
      }

      // Store results in localStorage for the results page
      window.localStorage.setItem("testScore", score.toString());
      window.localStorage.setItem("testPassed", passed.toString());
      window.localStorage.setItem("prevLevel", studentLevel);
      window.localStorage.setItem("studentLevel", newLevel);

      // Clear any saved question progress
      window.localStorage.removeItem('aiQuestionProgress');

      // Navigate to appropriate page
      if (passed) {
        router.push("/student/results");
      } else {
        router.push("/student/retry");
      }
    } catch (error) {
      console.error("Error saving test results:", error);
      toast.error("Gagal menyimpan hasil tes");
    } finally {
      setIsSaving(false);
    }
  };

  // Update loading time every second
  useEffect(() => {
    // Only start the timer if we're in the browser and loading
    if (isLoading && isBrowser) {
      const timer = setInterval(() => {
        setLoadingTime(prev => prev + 1);
      }, 1000);

      return () => clearInterval(timer);
    } else if (!isLoading) {
      // Reset timer when not loading
      setLoadingTime(0);
    }
  }, [isLoading, isBrowser]);

  // Function to skip loading and use mock questions
  const skipLoading = () => {
    console.log("User requested to skip loading");

    // Make sure we're in the browser
    if (!isBrowser) {
      console.log("Cannot skip loading in server-side rendering");
      return;
    }

    // Use mock questions
    useMockQuestions(studentLevel, 20);
    setIsLoading(false);
    toast.info("Menggunakan pertanyaan cadangan");
  };

  if (isLoading) {

    return (
      <div className="min-h-screen bg-gradient-to-b from-blue-100 to-purple-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center p-6">
          <h2 className="text-xl font-bold mb-4">
            Memuat pertanyaan dari bank soal...
          </h2>

          <div className="flex justify-center items-center gap-2 mb-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
          </div>

          {/* Loading time indicator */}
          <p className="text-xs text-gray-600 mt-1">
            Waktu memuat: {Math.floor(loadingTime / 60)}:{(loadingTime % 60).toString().padStart(2, '0')}
          </p>

          {/* Status indicator */}
          <div className="mt-4 flex items-center justify-center">
            <div className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Mengambil pertanyaan dari bank soal...
            </div>
          </div>

          {/* Information box */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-sm font-semibold text-blue-700 mb-1">Informasi:</h3>
            <ul className="text-xs text-blue-600 text-left list-disc pl-4 space-y-1">
              <li>Pertanyaan diambil dari bank soal</li>
              <li>Jika tidak ada pertanyaan tersedia, akan menggunakan pertanyaan cadangan</li>
              <li>Jika terlalu lama, gunakan tombol "Lewati" di bawah</li>
            </ul>
          </div>

          {/* Action buttons */}
          <div className="mt-4 flex justify-center gap-2">
            {/* Skip loading button - always visible */}
            <Button
              variant="outline"
              size="sm"
              onClick={skipLoading}
              className="text-xs"
            >
              {loadingTime > 10 ? "Lewati (Direkomendasikan)" : "Lewati"}
            </Button>
          </div>

          {/* Show warning if loading is taking too long */}
          {loadingTime > 15 && (
            <div className="mt-4 p-2 bg-red-50 border border-red-200 rounded-md text-xs text-red-600">
              Memuat pertanyaan membutuhkan waktu terlalu lama.
              Sebaiknya klik tombol "Lewati" untuk melanjutkan.
            </div>
          )}
        </Card>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-blue-100 to-purple-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center p-6">
          <h2 className="text-xl font-bold mb-4">Tidak ada pertanyaan tersedia</h2>
          <p className="mb-4">Maaf, tidak ada pertanyaan yang tersedia saat ini.</p>
          <Link href="/">
            <Button className="bg-blue-500 hover:bg-blue-600">
              Kembali ke Beranda
            </Button>
          </Link>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-100 to-purple-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-blue-200 shadow-xl">
          <CardHeader className="bg-blue-50 border-b border-blue-200">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl text-blue-700">
                Tes Utama: {studentLevel === "easy" ? "Mudah" : studentLevel === "medium" ? "Sedang" : "Sulit"}
              </CardTitle>
              <div className="flex items-center gap-4">
                <Link href="/" className="text-sm text-blue-600 hover:underline flex items-center">
                  <Home className="mr-1 h-4 w-4" />
                  Kembali ke Beranda
                </Link>
                <div className="text-sm text-blue-600 font-medium">
                  Pertanyaan {currentQuestionIndex + 1} dari {questions.length}
                </div>
              </div>
            </div>
            <Progress value={progress} className="h-2 bg-blue-100" />
          </CardHeader>

          {!showResult ? (
            <>
              <CardContent className="p-6">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-4">{currentQuestion.text}</h3>

                  <RadioGroup value={selectedAnswer || ""} className="space-y-3" disabled={showExplanation}>
                    {currentQuestion.options.map((option, index) => (
                      <div
                        key={index}
                        className={`flex items-center space-x-2 border-2 rounded-lg p-3 transition-colors ${
                          showExplanation
                            ? option === currentQuestion.correct_answer
                              ? "border-green-500 bg-green-50"
                              : option === selectedAnswer
                                ? "border-red-500 bg-red-50"
                                : "border-gray-200"
                            : "border-gray-200 hover:border-blue-300"
                        }`}
                      >
                        <RadioGroupItem
                          value={option}
                          id={`option-${index}`}
                          onClick={() => handleAnswerSelect(option)}
                          className={
                            showExplanation
                              ? option === currentQuestion.correct_answer
                                ? "text-green-600"
                                : "text-red-600"
                              : "text-blue-600"
                          }
                          disabled={showExplanation}
                        />
                        <Label htmlFor={`option-${index}`} className="flex-grow cursor-pointer py-1">
                          {option}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>

                  {showExplanation && (
                    <Alert
                      className={`mt-6 ${isCorrect ? "bg-green-50 border-green-200" : "bg-amber-50 border-amber-200"}`}
                    >
                      <AlertTitle className={isCorrect ? "text-green-700" : "text-amber-700"}>
                        {isCorrect ? "Benar!" : "Belum tepat"}
                      </AlertTitle>

                      <AlertDescription className="mt-2">
                        {currentQuestion.explanation}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </CardContent>

              <CardFooter className="bg-gray-50 border-t border-gray-200 p-4">
                {!showExplanation ? (
                  <Button
                    onClick={handleCheckAnswer}
                    disabled={selectedAnswer === null}
                    className="ml-auto bg-blue-500 hover:bg-blue-600"
                  >
                    Periksa Jawaban
                  </Button>
                ) : (
                  <Button
                    onClick={handleNextQuestion}
                    className="ml-auto bg-blue-500 hover:bg-blue-600"
                  >
                    {currentQuestionIndex < questions.length - 1 ? "Pertanyaan Berikutnya" : "Selesai Tes"}
                  </Button>
                )}
              </CardFooter>
            </>
          ) : (
            <CardContent className="p-6 text-center">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-blue-700 mb-2">Tes Selesai!</h3>
                <p className="text-gray-600 mb-4">
                  Kamu menjawab {score} dari {questions.length} pertanyaan dengan benar.
                </p>

                <div className="w-40 h-40 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">
                    {Math.round((score / questions.length) * 100)}%
                  </span>
                </div>

                <p className="text-lg font-medium text-gray-700 mb-6">
                  {(score / questions.length) * 100 >= kkm
                    ? "Selamat! Kamu telah lulus tes."
                    : "Kamu belum mencapai nilai kelulusan. Mari coba lagi!"}
                </p>

                <Button
                  onClick={handleFinishTest}
                  className="bg-purple-500 hover:bg-purple-600 px-8 py-2"
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Menyimpan...
                    </>
                  ) : (
                    (score / questions.length) * 100 >= kkm ? "Lihat Hasil" : "Coba Lagi"
                  )}
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
