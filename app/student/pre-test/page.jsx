"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { determineLevel } from "@/lib/api";
import { getPreTestQuestions, saveTestResult, updateStudentLevel } from "@/lib/api";
import { Home, Loader2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export default function PreTestPage() {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [answers, setAnswers] = useState([]);
  const [score, setScore] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        const preTestQuestions = await getPreTestQuestions();
        setQuestions(preTestQuestions);
        setAnswers(Array(preTestQuestions.length).fill(""));
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching pre-test questions:", error);
        toast.error("Gagal memuat pertanyaan");
        setIsLoading(false);
      }
    };

    fetchQuestions();
  }, []);

  const currentQuestion = questions[currentQuestionIndex];
  const progress = questions.length ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0;

  const handleAnswerSelect = (answer) => {
    setSelectedAnswer(answer);
  };

  const handleNextQuestion = () => {
    if (selectedAnswer === null || !currentQuestion) return;

    // Save the answer
    const newAnswers = [...answers];
    newAnswers[currentQuestionIndex] = selectedAnswer;
    setAnswers(newAnswers);

    // Update score if correct
    if (selectedAnswer === currentQuestion.correct_answer) {
      setScore(score + 1);
    }

    // Move to next question or show result
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
    } else {
      setShowResult(true);
    }
  };

  const handleStartMainTest = async () => {
    try {
      const studentId = localStorage.getItem("studentId");
      if (!studentId) {
        toast.error("ID Siswa tidak ditemukan. Silakan masuk kembali.");
        router.push("/student/login");
        return;
      }

      const level = determineLevel(score, questions.length);

      // Save test result to database
      await saveTestResult({
        student_id: studentId,
        test_type: 'pre-test',
        score,
        total_questions: questions.length,
        passed: true, // Pre-test is always passed
        new_level: level,
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        subject: "science",
        topic: "energy transformation"
      });

      // Update student level
      await updateStudentLevel(studentId, level);

      // Update localStorage
      localStorage.setItem("studentLevel", level);
      localStorage.setItem("preTestScore", score.toString());

      router.push("/student/main-test");
    } catch (error) {
      console.error("Error saving test results:", error);
      toast.error("Gagal menyimpan hasil tes");
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <CardContent className="p-6 flex justify-center items-center min-h-[300px]">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-green-500 mb-2" />
            <p className="text-gray-600">Memuat pertanyaan...</p>
          </div>
        </CardContent>
      );
    }

    if (!showResult && currentQuestion) {
      return (
        <>
          <CardContent className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-4">{currentQuestion.text}</h3>

              <RadioGroup value={selectedAnswer || ""} className="space-y-3">
                {currentQuestion.options.map((option, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 border-2 border-gray-200 rounded-lg p-3 hover:border-green-300 transition-colors"
                  >
                    <RadioGroupItem
                      value={option}
                      id={`option-${index}`}
                      onClick={() => handleAnswerSelect(option)}
                      className="text-green-600"
                    />
                    <Label htmlFor={`option-${index}`} className="flex-grow cursor-pointer py-1">
                      {option}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          </CardContent>

          <CardFooter className="bg-gray-50 border-t border-gray-200 p-4">
            <Button
              onClick={handleNextQuestion}
              disabled={selectedAnswer === null}
              className="ml-auto bg-green-500 hover:bg-green-600"
            >
              {currentQuestionIndex < questions.length - 1 ? "Pertanyaan Berikutnya" : "Selesai Pra-Tes"}
            </Button>
          </CardFooter>
        </>
      );
    }

    if (!isLoading && showResult) {
      return (
        <CardContent className="p-6 text-center">
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-green-700 mb-2">Pra-Tes Selesai!</h3>
            <p className="text-gray-600 mb-4">
              Kamu menjawab {score} dari {questions.length} pertanyaan dengan benar.
            </p>

            <div className="w-40 h-40 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-6">
              <span className="text-4xl font-bold text-green-600">
                {questions.length ? Math.round((score / questions.length) * 100) : 0}%
              </span>
            </div>

            <p className="text-lg font-medium text-gray-700 mb-6">
              Berdasarkan nilaimu, kamu akan melanjutkan ke tes utama dengan pertanyaan sesuai tingkat kemampuanmu.
            </p>

            <Button
              onClick={handleStartMainTest}
              className="bg-blue-500 hover:bg-blue-600 px-8 py-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                "Mulai Tes Utama"
              )}
            </Button>
          </div>
        </CardContent>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-100 to-blue-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-green-200 shadow-xl">
          <CardHeader className="bg-green-50 border-b border-green-200">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl text-green-700">Penilaian Pra-Tes</CardTitle>
              <div className="flex items-center gap-4">
                <Link href="/" className="text-sm text-green-600 hover:underline flex items-center">
                  <Home className="mr-1 h-4 w-4" />
                  Kembali ke Beranda
                </Link>
                {!isLoading && questions.length > 0 && (
                  <div className="text-sm text-green-600 font-medium">
                    Pertanyaan {currentQuestionIndex + 1} dari {questions.length}
                  </div>
                )}
              </div>
            </div>
            <Progress value={progress} className="h-2 bg-green-100" />
          </CardHeader>

          {renderContent()}
        </Card>
      </div>
    </div>
  );
}
