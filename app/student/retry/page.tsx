"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle } from "lucide-react"

export default function RetryPage() {
  const [score, setScore] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(20)
  const [level, setLevel] = useState<string>("")
  const router = useRouter()

  useEffect(() => {
    // In a real app, we would fetch this from the backend
    const testScore = localStorage.getItem("testScore") || "0"
    const studentLevel = localStorage.getItem("studentLevel") || "easy"

    setScore(Number.parseInt(testScore))
    setLevel(studentLevel)
  }, [])

  const handleRetry = () => {
    // Store the current level as previous level
    localStorage.setItem("prevLevel", level)
    router.push("/student/main-test")
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-100 to-red-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-orange-300 shadow-xl">
          <CardHeader className="bg-orange-50 border-b border-orange-200 text-center">
            <div className="mx-auto w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-orange-600" />
            </div>
            <CardTitle className="text-3xl text-orange-700">Let's Try Again</CardTitle>
          </CardHeader>

          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">You Need More Practice</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white p-4 rounded-lg shadow border border-orange-200">
                <div className="text-4xl font-bold text-orange-600 mb-2">{score}</div>
                <div className="text-gray-600">Questions Correct</div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow border border-orange-200">
                <div className="text-4xl font-bold text-orange-600 mb-2">
                  {Math.round((score / totalQuestions) * 100)}%
                </div>
                <div className="text-gray-600">Score Percentage</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
              <p className="text-blue-700">
                You'll continue at the{" "}
                <span className="font-bold">{level.charAt(0).toUpperCase() + level.slice(1)}</span> level. Don't worry,
                you'll get a new set of questions!
              </p>
            </div>

            <p className="text-gray-600 mb-8">
              Energy transformation can be tricky! Let's try again with a new set of questions at your current level.
              Remember, each form of energy can change into another form.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={handleRetry} className="bg-orange-500 hover:bg-orange-600 px-6">
                Try Again
              </Button>

              <Link href="/">
                <Button
                  variant="outline"
                  className="border-2 border-orange-300 text-orange-600 hover:bg-orange-50 px-6"
                >
                  Back to Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
