"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { createStudent, getStudentByStudentId } from "@/lib/api"

export default function StudentLoginPage() {
  const [studentId, setStudentId] = useState("")
  const [name, setName] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!studentId || !name) return

    setIsLoading(true)

    try {
      // Check if student exists
      const existingStudent = await getStudentByStudentId(studentId)

      if (existingStudent) {
        // If student exists, update localStorage and redirect
        localStorage.setItem("studentId", existingStudent.student_id)
        localStorage.setItem("studentName", existingStudent.name)
        router.push("/student/pre-test")
      } else {
        // If student doesn't exist, create a new one
        const newStudent = await createStudent({
          name,
          student_id: studentId,
          current_level: 'easy',
          created_at: new Date().toISOString()
        })

        if (newStudent) {
          localStorage.setItem("studentId", newStudent.student_id)
          localStorage.setItem("studentName", newStudent.name)
          router.push("/student/pre-test")
        } else {
          toast.error("Gagal membuat akun siswa")
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      toast.error("Terjadi kesalahan saat login")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-100 to-green-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md border-2 border-blue-200 shadow-xl">
        <CardHeader className="text-center bg-blue-50 rounded-t-lg">
          <CardTitle className="text-2xl text-blue-700">Login Siswa</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="studentId" className="text-blue-700">
                ID Siswa
              </Label>
              <Input
                id="studentId"
                type="text"
                placeholder="Masukkan ID siswa kamu"
                value={studentId}
                onChange={(e) => setStudentId(e.target.value)}
                className="border-2 border-blue-200 focus:border-blue-400"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="name" className="text-blue-700">
                Nama Kamu
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Masukkan nama lengkap kamu"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="border-2 border-blue-200 focus:border-blue-400"
                required
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-blue-500 hover:bg-blue-600 mt-6"
              disabled={isLoading}
            >
              {isLoading ? "Memuat..." : "Mulai Penilaian"}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <Link href="/" className="text-blue-600 hover:underline">
              Kembali ke Beranda
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
