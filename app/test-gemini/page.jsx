"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { generateQuestionWithGemini, generateExplanationWithGemini } from "@/lib/gemini-ai";
import { Loader2, <PERSON><PERSON><PERSON> } from "lucide-react";

export default function TestGeminiPage() {
  const [question, setQuestion] = useState(null);
  const [explanation, setExplanation] = useState("");
  const [isGeneratingQuestion, setIsGeneratingQuestion] = useState(false);
  const [isGeneratingExplanation, setIsGeneratingExplanation] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState("easy");
  const [selectedAnswer, setSelectedAnswer] = useState("");
  const [isCorrect, setIsCorrect] = useState(false);

  const handleGenerateQuestion = async () => {
    setIsGeneratingQuestion(true);
    try {
      const generatedQuestion = await generateQuestionWithGemini(
        selectedLevel,
        "energy transformation",
        "science"
      );
      setQuestion(generatedQuestion);
      setSelectedAnswer("");
      setExplanation("");
    } catch (error) {
      console.error("Error generating question:", error);
      alert("Failed to generate question");
    } finally {
      setIsGeneratingQuestion(false);
    }
  };

  const handleGenerateExplanation = async () => {
    if (!question || !selectedAnswer) {
      alert("Please select an answer first");
      return;
    }

    setIsGeneratingExplanation(true);
    const correct = selectedAnswer === question.correct_answer;
    setIsCorrect(correct);

    try {
      // No custom explanation prompt for the test page
      const generatedExplanation = await generateExplanationWithGemini(
        question.text,
        selectedAnswer,
        question.correct_answer,
        correct,
        selectedLevel,
        null // No custom explanation prompt
      );
      setExplanation(generatedExplanation);
    } catch (error) {
      console.error("Error generating explanation:", error);
      alert("Failed to generate explanation");
    } finally {
      setIsGeneratingExplanation(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-100 to-purple-100 p-4">
      <div className="container mx-auto max-w-3xl">
        <Card className="border-2 border-blue-200 shadow-xl">
          <CardHeader className="bg-blue-50 border-b border-blue-200">
            <CardTitle className="text-2xl text-blue-700 flex items-center">
              Test Gemini AI Integration
              <Sparkles className="ml-2 h-5 w-5 text-yellow-500" />
            </CardTitle>
          </CardHeader>

          <CardContent className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-4">Generate AI Questions and Explanations</h3>

              <div className="flex gap-4 mb-6">
                <Button
                  variant={selectedLevel === "easy" ? "default" : "outline"}
                  onClick={() => setSelectedLevel("easy")}
                  className={selectedLevel === "easy" ? "bg-green-500 hover:bg-green-600" : ""}
                >
                  Easy
                </Button>
                <Button
                  variant={selectedLevel === "medium" ? "default" : "outline"}
                  onClick={() => setSelectedLevel("medium")}
                  className={selectedLevel === "medium" ? "bg-blue-500 hover:bg-blue-600" : ""}
                >
                  Medium
                </Button>
                <Button
                  variant={selectedLevel === "hard" ? "default" : "outline"}
                  onClick={() => setSelectedLevel("hard")}
                  className={selectedLevel === "hard" ? "bg-purple-500 hover:bg-purple-600" : ""}
                >
                  Hard
                </Button>
              </div>

              <Button
                onClick={handleGenerateQuestion}
                disabled={isGeneratingQuestion}
                className="w-full bg-yellow-500 hover:bg-yellow-600 mb-6"
              >
                {isGeneratingQuestion ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Question...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)} Question
                  </>
                )}
              </Button>

              {question && (
                <div className="border-2 border-blue-100 rounded-lg p-4 mb-6">
                  <h4 className="font-bold mb-2">{question.text}</h4>
                  <div className="space-y-2">
                    {question.options.map((option, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border-2 cursor-pointer ${
                          selectedAnswer === option
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-blue-300"
                        }`}
                        onClick={() => setSelectedAnswer(option)}
                      >
                        {option}
                      </div>
                    ))}
                  </div>

                  <div className="mt-4">
                    <Button
                      onClick={handleGenerateExplanation}
                      disabled={!selectedAnswer || isGeneratingExplanation}
                      className="w-full bg-green-500 hover:bg-green-600"
                    >
                      {isGeneratingExplanation ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating Explanation...
                        </>
                      ) : (
                        "Check Answer & Generate Explanation"
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {explanation && (
                <div
                  className={`border-2 rounded-lg p-4 ${
                    isCorrect ? "border-green-200 bg-green-50" : "border-amber-200 bg-amber-50"
                  }`}
                >
                  <h4 className={`font-bold mb-2 ${isCorrect ? "text-green-700" : "text-amber-700"}`}>
                    {isCorrect ? "Correct!" : "Incorrect"} - AI Explanation:
                  </h4>
                  <p>{explanation}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
