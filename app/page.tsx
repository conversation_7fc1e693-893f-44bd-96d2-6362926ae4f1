import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-200 via-purple-100 to-yellow-100 relative overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-16 h-16 bg-yellow-300 rounded-full animate-pulse opacity-70"></div>
      <div className="absolute top-40 right-20 w-20 h-20 bg-green-300 rounded-full animate-bounce opacity-60"></div>
      <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-blue-300 rounded-full animate-pulse opacity-50"></div>
      <div className="absolute top-1/3 right-1/3 w-12 h-12 bg-red-300 rounded-full animate-bounce opacity-60"></div>

      {/* Lightning bolt decorations */}
      <div className="absolute top-10 right-10 text-yellow-400 text-5xl transform rotate-12">⚡</div>
      <div className="absolute bottom-10 left-10 text-yellow-400 text-4xl transform -rotate-12">⚡</div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-block bg-white/80 backdrop-blur-sm rounded-full px-8 py-3 shadow-lg transform hover:scale-105 transition-transform">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-orange-500 bg-clip-text text-transparent">
                Perubahan Energi
              </h1>
              <p className="text-xl md:text-2xl text-gray-700 mt-2">Petualangan Sains Kelas 5!</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="relative mb-12">
            <div className="bg-white/70 backdrop-blur-sm rounded-3xl p-6 shadow-xl border-4 border-blue-300">
              {/* Energy Illustration */}
              <div className="relative h-64 md:h-80 mb-8 overflow-hidden rounded-2xl">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-purple-200 opacity-50"></div>

                {/* Energy Icons */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="grid grid-cols-3 gap-4 w-full max-w-2xl px-4">
                    {/* Light Energy */}
                    <div className="bg-yellow-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-yellow-300">
                      <div className="text-4xl mb-2">💡</div>
                      <p className="font-medium text-yellow-700">Energi Cahaya</p>
                    </div>

                    {/* Heat Energy */}
                    <div className="bg-red-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-red-300">
                      <div className="text-4xl mb-2">🔥</div>
                      <p className="font-medium text-red-700">Energi Panas</p>
                    </div>

                    {/* Motion Energy */}
                    <div className="bg-blue-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-blue-300">
                      <div className="text-4xl mb-2">🔄</div>
                      <p className="font-medium text-blue-700">Energi Gerak</p>
                    </div>

                    {/* Sound Energy */}
                    <div className="bg-purple-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-purple-300">
                      <div className="text-4xl mb-2">🔊</div>
                      <p className="font-medium text-purple-700">Energi Bunyi</p>
                    </div>

                    {/* Electrical Energy */}
                    <div className="bg-cyan-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-cyan-300">
                      <div className="text-4xl mb-2">⚡</div>
                      <p className="font-medium text-cyan-700">Energi Listrik</p>
                    </div>

                    {/* Chemical Energy */}
                    <div className="bg-green-100 rounded-xl p-4 text-center transform hover:scale-105 transition-transform shadow-md border-2 border-green-300">
                      <div className="text-4xl mb-2">🧪</div>
                      <p className="font-medium text-green-700">Energi Kimia</p>
                    </div>
                  </div>
                </div>
              </div>

              <p className="text-xl text-center mb-8 text-gray-700 bg-yellow-100 p-4 rounded-xl border-2 border-yellow-200 shadow-inner">
                <span className="font-bold text-orange-600">Wah!</span> Tahukah kamu energi bisa berubah dari satu bentuk ke
                bentuk lainnya? Ayo kita jelajahi bagaimana energi berubah dan menggerakkan dunia kita!
              </p>

              {/* Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-4 border-blue-300 shadow-lg hover:shadow-xl transition-all transform hover:scale-105 overflow-hidden">
                  <div className="absolute top-0 right-0 bg-blue-400 text-white px-3 py-1 rounded-bl-lg text-sm font-bold">
                    Untuk Siswa
                  </div>
                  <CardContent className="p-6 pt-10">
                    <div className="text-center mb-4">
                      <div className="inline-block p-3 bg-blue-200 rounded-full mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="40"
                          height="40"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-blue-600"
                        >
                          <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                          <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
                        </svg>
                      </div>
                      <h2 className="text-2xl font-bold text-blue-700 mb-2">Zona Siswa</h2>
                    </div>
                    <p className="mb-6 text-gray-700 text-center">
                      Siap menguji pengetahuanmu tentang perubahan energi? Ikuti penilaian yang seru dan lihat berapa banyak yang kamu tahu!
                    </p>
                    <Link href="/student/login">
                      <Button className="w-full bg-blue-500 hover:bg-blue-600 text-lg py-6 rounded-xl">
                        Mulai Petualanganmu!
                      </Button>
                    </Link>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-4 border-purple-300 shadow-lg hover:shadow-xl transition-all transform hover:scale-105 overflow-hidden">
                  <div className="absolute top-0 right-0 bg-purple-400 text-white px-3 py-1 rounded-bl-lg text-sm font-bold">
                    Untuk Guru
                  </div>
                  <CardContent className="p-6 pt-10">
                    <div className="text-center mb-4">
                      <div className="inline-block p-3 bg-purple-200 rounded-full mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="40"
                          height="40"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-purple-600"
                        >
                          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="3" y1="9" x2="21" y2="9"></line>
                          <line x1="9" y1="21" x2="9" y2="9"></line>
                        </svg>
                      </div>
                      <h2 className="text-2xl font-bold text-purple-700 mb-2">Dasbor Guru</h2>
                    </div>
                    <p className="mb-6 text-gray-700 text-center">
                      Kelola pertanyaan, pantau kemajuan siswa, dan sesuaikan penilaian untuk kelas Anda!
                    </p>
                    <Link href="/teacher/login">
                      <Button className="w-full bg-purple-500 hover:bg-purple-600 text-lg py-6 rounded-xl">
                        Login Guru
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>

              {/* Demo Button */}
              <div className="text-center">
                <div className="inline-block relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-pink-400 rounded-lg blur opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse"></div>
                  <Link href="/demo" className="relative">
                    <Button
                      variant="outline"
                      className="border-3 border-orange-300 bg-white text-orange-600 hover:bg-orange-50 text-lg px-8 py-6 rounded-xl"
                    >
                      Coba Pertanyaan Demo!
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-gray-600 text-sm">
            <p>© 2023 Penilaian Perubahan Energi | Dibuat dengan ❤️ untuk Ilmuwan Kelas 5</p>
          </div>
        </div>
      </div>
    </div>
  )
}
