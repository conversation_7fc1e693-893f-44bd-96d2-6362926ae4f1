"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, Settings, Users, <PERSON>Chart, LogOut } from "lucide-react"
import { StudentManagement } from "@/components/teacher/student-management"
import { ReportsDashboard } from "@/components/teacher/reports-dashboard"
import { SettingsComponent } from "@/components/teacher/settings-component"
import { QuestionManagement } from "@/components/teacher/question-management"
import { useRouter } from "next/navigation"

export default function TeacherDashboardPage() {
  const [activeTab, setActiveTab] = useState("questions")
  const router = useRouter()

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-100 to-pink-100">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-64">
            <Card className="border-2 border-purple-200 shadow-lg">
              <CardHeader className="bg-purple-50 border-b border-purple-200">
                <CardTitle className="text-xl text-purple-700">Teacher Dashboard</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <nav className="space-y-2">
                  <Button
                    variant={activeTab === "questions" ? "default" : "ghost"}
                    className={`w-full justify-start ${activeTab === "questions" ? "bg-purple-500 hover:bg-purple-600 text-white" : ""}`}
                    onClick={() => handleTabChange("questions")}
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Questions
                  </Button>
                  <Button
                    variant={activeTab === "students" ? "default" : "ghost"}
                    className={`w-full justify-start ${activeTab === "students" ? "bg-purple-500 hover:bg-purple-600 text-white" : ""}`}
                    onClick={() => handleTabChange("students")}
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Students
                  </Button>
                  <Button
                    variant={activeTab === "reports" ? "default" : "ghost"}
                    className={`w-full justify-start ${activeTab === "reports" ? "bg-purple-500 hover:bg-purple-600 text-white" : ""}`}
                    onClick={() => handleTabChange("reports")}
                  >
                    <BarChart className="mr-2 h-4 w-4" />
                    Reports
                  </Button>
                  <Button
                    variant={activeTab === "settings" ? "default" : "ghost"}
                    className={`w-full justify-start ${activeTab === "settings" ? "bg-purple-500 hover:bg-purple-600 text-white" : ""}`}
                    onClick={() => handleTabChange("settings")}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </nav>

                <div className="mt-8 pt-4 border-t border-purple-200">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
                    onClick={() => router.push("/")}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <TabsList className="mb-6 bg-white border-2 border-purple-200">
                <TabsTrigger value="questions">Question Bank</TabsTrigger>
                <TabsTrigger value="students">Students</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="questions">
                <Card className="border-2 border-purple-200 shadow-lg">
                  <CardHeader className="bg-purple-50 border-b border-purple-200">
                    <CardTitle className="text-xl text-purple-700">Energy Transformation Questions</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <QuestionManagement />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="students">
                <Card className="border-2 border-purple-200 shadow-lg">
                  <CardHeader className="bg-purple-50 border-b border-purple-200">
                    <CardTitle className="text-xl text-purple-700">Student Management</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <StudentManagement />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reports">
                <Card className="border-2 border-purple-200 shadow-lg">
                  <CardHeader className="bg-purple-50 border-b border-purple-200">
                    <CardTitle className="text-xl text-purple-700">Assessment Reports</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <ReportsDashboard />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings">
                <Card className="border-2 border-purple-200 shadow-lg">
                  <CardHeader className="bg-purple-50 border-b border-purple-200">
                    <CardTitle className="text-xl text-purple-700">System Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <SettingsComponent />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
