# Versioning and metadata
.git
.gitignore
.github

# Build dependencies
node_modules
.pnpm-store

# Environment (contains sensitive data)
.env
.env.*
!.env.example
!.env.production

# Files not required for production
README.md
CHANGELOG.md
docker-compose.yml
Dockerfile
.dockerignore
.eslintrc.json
.prettierrc
.editorconfig

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local development
.DS_Store
*.pem

# Testing
coverage
.nyc_output

# Misc
.vscode
.idea
