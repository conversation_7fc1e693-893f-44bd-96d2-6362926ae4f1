# Supabase Setup Guide for Science Assessment App

This guide will walk you through the process of setting up Supabase for the Science Assessment App, including authentication and database configuration.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Node.js installed on your computer
- The Science Assessment App codebase

## Step 1: Create a Supabase Project

1. Log in to your Supabase account at [app.supabase.com](https://app.supabase.com)
2. Click "New Project"
3. Enter a name for your project (e.g., "Science Assessment App")
4. Set a secure database password (keep this safe, you'll need it if you want to access the database directly)
5. Choose a region close to your users
6. Click "Create new project"

## Step 2: Get Your Supabase Credentials

1. Once your project is created, go to the project dashboard
2. Click on "Settings" in the left sidebar
3. Click on "API" in the submenu
4. You'll see your "Project URL" and "anon" key (public API key)
5. Copy these values as you'll need them in the next step

## Step 3: Configure Environment Variables

1. In your Science Assessment App project, create or edit the `.env.local` file
2. Add the following environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

Replace `your-project-url` and `your-anon-key` with the values you copied in Step 2.

## Step 4: Set Up Database Schema

The app includes a SQL script to set up all necessary tables and configurations:

1. Go to your Supabase dashboard
2. Click on "SQL Editor" in the left sidebar
3. Click "New query"
4. Open the file `scripts/supabase-schema.sql` in your project
5. Copy the entire contents of this file
6. Paste it into the SQL Editor in Supabase
7. Click "Run" to execute the script

Alternatively, you can use the helper script to guide you through this process:

```bash
node scripts/setup-database.js
```

This script will provide instructions and can show you the SQL schema to copy.

## Step 5: Configure Authentication Settings

1. In your Supabase dashboard, click on "Authentication" in the left sidebar
2. Click on "Settings" in the submenu
3. Under "Email Auth", make sure "Enable Email Signup" is toggled ON
4. For development purposes, you might want to disable email confirmations:
   - Set "Enable email confirmations" to OFF (this makes testing easier)
5. Configure other settings as needed:
   - Password strength requirements
   - Allowed email domains (if you want to restrict signups)
6. Click "Save" at the bottom of the page

## Step 6: Create a Teacher Account

The app includes a script to create a teacher account:

```bash
node scripts/setup-auth.js
```

This script will:
1. Prompt you for a teacher email, name, and password
2. Create an authentication user in Supabase
3. Create a corresponding entry in the `teachers` table
4. Display the user ID and teacher ID upon successful creation

If you encounter any issues with the script, you can create a teacher account manually:

1. Go to "Authentication" > "Users" in your Supabase dashboard
2. Click "Add User"
3. Enter an email and password
4. Click "Create User"
5. Note the user ID (UUID)
6. Go to "Table Editor" > "teachers"
7. Click "Insert row"
8. Fill in:
   - `auth_id`: The UUID from the created user
   - `name`: The teacher's name
   - `email`: The same email used for authentication
9. Click "Save"

## Step 7: Seed Initial Questions

To populate the database with initial questions:

```bash
node scripts/seed-questions.js
```

This script will add pre-test and main-test questions to the database.

## Step 8: Test the Setup

1. Start your application:

```bash
pnpm dev
```

2. Try logging in with the teacher account you created
3. Create a student account and take a test
4. Verify that data is being saved to the database

## Troubleshooting

### Authentication Issues

- **Error: "User not found"**: Make sure the email and password match what you created
- **Error: "Invalid login credentials"**: Check that you've entered the correct password
- **Error: "Email not confirmed"**: If you have email confirmations enabled, check your email for a confirmation link

### Database Issues

- **Error: "relation does not exist"**: Make sure you've run the schema SQL script successfully
- **Error: "permission denied"**: Check that your Row Level Security (RLS) policies are correctly set up
- **Error: "foreign key constraint violation"**: Ensure related records exist before creating dependent records

### Script Issues

- **Error: "Cannot use import statement outside a module"**: Make sure you're using the CommonJS version of the scripts
- **Error: "Missing Supabase environment variables"**: Check that your `.env.local` file has the correct values

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript/introduction)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Supabase Database Documentation](https://supabase.com/docs/guides/database)

## Next Steps

After setting up Supabase, you might want to:

1. Customize the application settings in the `settings` table
2. Add more questions to the database
3. Create additional teacher accounts
4. Set up classes and assign students to them

For any further assistance, refer to the project documentation or contact the development team.
