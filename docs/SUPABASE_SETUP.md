# Supabase Setup for Science Assessment App

This guide will help you set up Supabase as the database for the Science Assessment App.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Node.js and pnpm installed

## Step 1: Create a Supabase Project

1. Log in to your Supabase account
2. Create a new project
3. Note your project URL and anon key (public API key)

## Step 2: Configure Environment Variables

1. Create a `.env.local` file in the root of your project (if not already created)
2. Add the following environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

Replace `your-supabase-url` and `your-supabase-anon-key` with the values from your Supabase project.

## Step 3: Set Up Database Schema

1. Go to the SQL Editor in your Supabase dashboard
2. Create a new query
3. Copy and paste the contents of `scripts/supabase-schema.sql`
4. Run the query to create the necessary tables and indexes

## Step 4: Set Up Authentication

1. In your Supabase dashboard, go to Authentication > Settings
2. Under <PERSON>ail <PERSON>th, make sure "Enable Email Signup" is turned on
3. Optionally, you can disable email confirmations for easier testing

4. Create a teacher account using the provided script:
   ```
   pnpm add dotenv readline
   node scripts/setup-auth.js
   ```

   Follow the prompts to create a teacher account with email and password.

## Step 5: Seed Initial Data

To seed your database with initial questions:

1. Install the required dependencies (if not already installed):
   ```
   pnpm add dotenv
   ```

2. Run the seed script:
   ```
   node scripts/seed-questions.js
   ```

## Step 6: Run the Application

Start the development server:

```
pnpm dev
```

## Database Schema

The application uses the following tables:

### questions
- `id`: UUID (primary key)
- `text`: TEXT (question text)
- `options`: JSONB (array of answer options)
- `correct_answer`: TEXT (the correct answer)
- `difficulty`: TEXT ('easy', 'medium', or 'hard')
- `explanation`: TEXT (explanation of the answer)
- `created_at`: TIMESTAMP
- `updated_at`: TIMESTAMP
- `question_type`: TEXT ('pre-test' or 'main-test')

### students
- `id`: UUID (primary key)
- `name`: TEXT (student name)
- `student_id`: TEXT (unique student identifier)
- `current_level`: TEXT ('easy', 'medium', or 'hard')
- `created_at`: TIMESTAMP
- `updated_at`: TIMESTAMP

### test_results
- `id`: UUID (primary key)
- `student_id`: TEXT (references students.student_id)
- `test_type`: TEXT ('pre-test' or 'main-test')
- `score`: INTEGER (number of correct answers)
- `total_questions`: INTEGER (total number of questions)
- `passed`: BOOLEAN (whether the student passed the test)
- `previous_level`: TEXT (student's level before the test)
- `new_level`: TEXT (student's level after the test)
- `created_at`: TIMESTAMP

### settings
- `id`: UUID (primary key)
- `kkm`: INTEGER (minimum passing score percentage)
- `pre_test_questions`: INTEGER (number of pre-test questions)
- `main_test_questions`: INTEGER (number of main-test questions)
- `enable_ai`: BOOLEAN (whether AI explanations are enabled)
- `ai_model`: TEXT (AI model to use for explanations)
- `ai_prompt`: TEXT (prompt template for AI explanations)
- `created_at`: TIMESTAMP
- `updated_at`: TIMESTAMP
