# GitHub Repository Setup Instructions

1. Go to GitHub (https://github.com) and sign in to your account.
2. Click on the "+" icon in the top-right corner and select "New repository".
3. Name your repository (e.g., "science-assessment-app").
4. You can add an optional description.
5. Choose whether to make it public or private.
6. Do NOT initialize the repository with a README, .gitignore, or license since we already have our code.
7. Click "Create repository".

After creating the repository, GitHub will show you commands to push your existing repository. You'll need to run commands similar to these (replace the URL with your actual repository URL):

```bash
git remote add origin https://github.com/YOUR-USERNAME/science-assessment-app.git
git branch -M main
git push -u origin main
```

Replace `YOUR-USERNAME` with your actual GitHub username.
