# Science Assessment App - Supabase Database Setup

This document provides instructions on how to set up the Supabase database for the Science Assessment App.

## Database Schema Overview

The Science Assessment App uses the following tables:

1. **questions** - Stores assessment questions
2. **students** - Stores student information
3. **teachers** - Stores teacher information
4. **classes** - Stores class information
5. **student_classes** - Manages the many-to-many relationship between students and classes
6. **test_results** - Stores test results
7. **student_answers** - Stores individual student answers to questions
8. **settings** - Stores application settings

## Setting Up the Database

### Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Access to the Supabase dashboard

### Step 1: Create a New Supabase Project

1. Log in to your Supabase account
2. Click "New Project"
3. Enter a name for your project
4. Set a secure database password
5. Choose a region close to your users
6. Click "Create new project"

### Step 2: Run the SQL Script

1. Once your project is created, go to the SQL Editor in the Supabase dashboard
2. Click "New query"
3. Copy the entire contents of the `scripts/supabase-schema.sql` file
4. Paste it into the SQL Editor
5. Click "Run" to execute the script

The script will:
- Create all necessary tables
- Set up relationships between tables
- Create indexes for better performance
- Set up Row Level Security (RLS) policies
- Create triggers for automatic timestamp updates

### Step 3: Configure Authentication

1. In the Supabase dashboard, go to Authentication > Settings
2. Under Email Auth, make sure "Enable Email Signup" is turned on
3. Optionally, you can disable email confirmations for easier testing

### Step 4: Create a Teacher Account

You can create a teacher account using the provided script:

```bash
# Install dependencies
pnpm add dotenv readline

# Run the script
node scripts/setup-auth.js
```

Follow the prompts to create a teacher account with email and password.

### Step 5: Seed Initial Data

To populate the database with initial questions:

```bash
# Install dependencies if not already installed
pnpm add dotenv

# Run the seed script
node scripts/seed-questions.js
```

## Database Structure Details

### questions

Stores assessment questions with their options, correct answers, and explanations.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| text | TEXT | Question text |
| options | JSONB | Array of answer options |
| correct_answer | TEXT | The correct answer |
| difficulty | TEXT | 'easy', 'medium', or 'hard' |
| explanation | TEXT | Explanation of the answer |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| question_type | TEXT | 'pre-test' or 'main-test' |
| subject | TEXT | Subject area (default: 'science') |
| topic | TEXT | Topic within subject (default: 'energy transformation') |
| grade_level | INTEGER | Grade level (default: 5) |

### students

Stores student information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | TEXT | Student name |
| student_id | TEXT | Unique student identifier |
| current_level | TEXT | 'easy', 'medium', or 'hard' |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| grade | INTEGER | Student's grade level |
| class_section | TEXT | Class section |
| last_login | TIMESTAMP | Last login timestamp |

### teachers

Stores teacher information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| auth_id | UUID | Reference to auth.users |
| name | TEXT | Teacher name |
| email | TEXT | Teacher email |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### classes

Stores class information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | TEXT | Class name |
| teacher_id | UUID | Reference to teachers |
| grade_level | INTEGER | Grade level |
| section | TEXT | Section identifier |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### student_classes

Manages the many-to-many relationship between students and classes.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| student_id | UUID | Reference to students |
| class_id | UUID | Reference to classes |
| created_at | TIMESTAMP | Creation timestamp |

### test_results

Stores test results.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| student_id | TEXT | Reference to students |
| test_type | TEXT | 'pre-test' or 'main-test' |
| score | INTEGER | Number of correct answers |
| total_questions | INTEGER | Total number of questions |
| passed | BOOLEAN | Whether the student passed |
| previous_level | TEXT | Student's level before the test |
| new_level | TEXT | Student's level after the test |
| created_at | TIMESTAMP | Creation timestamp |
| completed_at | TIMESTAMP | Completion timestamp |
| time_spent | INTEGER | Time spent in seconds |
| subject | TEXT | Subject area |
| topic | TEXT | Topic within subject |

### student_answers

Stores individual student answers to questions.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| test_result_id | UUID | Reference to test_results |
| question_id | UUID | Reference to questions |
| student_answer | TEXT | Student's answer |
| is_correct | BOOLEAN | Whether the answer is correct |
| time_spent | INTEGER | Time spent in seconds |
| created_at | TIMESTAMP | Creation timestamp |

### settings

Stores application settings.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| kkm | INTEGER | Minimum passing score percentage |
| pre_test_questions | INTEGER | Number of pre-test questions |
| main_test_questions | INTEGER | Number of main-test questions |
| enable_ai | BOOLEAN | Whether AI explanations are enabled |
| ai_model | TEXT | AI model to use for explanations |
| ai_prompt | TEXT | Prompt template for AI explanations |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| school_name | TEXT | School name |
| school_logo_url | TEXT | URL to school logo |
| theme_color | TEXT | Theme color |
| enable_student_registration | BOOLEAN | Whether student registration is enabled |

## Row Level Security (RLS)

The database uses Row Level Security to ensure data privacy:

- Questions are accessible by all authenticated users
- Students can only view their own data, while teachers can see all student data
- Test results are viewable by the student who took the test or any teacher
- Settings are viewable by all authenticated users but only updatable by teachers

## Automatic Timestamps

The database uses triggers to automatically update the `updated_at` column whenever a record is updated.

## Indexes

Indexes are created on frequently queried columns to improve performance.

## Next Steps

After setting up the database, you'll need to:

1. Configure your application to connect to Supabase
2. Update your environment variables with your Supabase URL and API key
3. Test the application to ensure it's correctly interacting with the database

For more information on using Supabase with your application, refer to the [Supabase documentation](https://supabase.com/docs).
