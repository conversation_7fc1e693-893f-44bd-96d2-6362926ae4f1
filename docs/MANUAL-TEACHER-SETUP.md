# Manual Teacher Account Setup Guide

If you're experiencing issues with the automated script, you can follow these steps to manually create a teacher account in Supabase.

## Step 1: Create a User in Supabase Authentication

1. **Go to your Supabase Dashboard**:
   - Navigate to [https://app.supabase.com/](https://app.supabase.com/)
   - Select your project

2. **Create a New User**:
   - In the left sidebar, click on "Authentication"
   - Click on "Users"
   - Click the "Add User" button
   - Enter an email address and password
   - Click "Create User"

3. **Note the User ID**:
   - After creating the user, click on the user in the list
   - Copy the "User UID" (it's a UUID format like `a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6`)
   - You'll need this ID for the next step

## Step 2: Create a Teacher Record in the Database

1. **Go to the Table Editor**:
   - In the left sidebar, click on "Table Editor"
   - Select the "teachers" table from the list

2. **Insert a New Teacher Record**:
   - Click the "Insert Row" button
   - Fill in the following fields:
     - `auth_id`: Paste the User UID you copied in Step 1
     - `name`: Enter the teacher's name
     - `email`: Enter the same email address you used in Step 1
   - Leave the other fields as they are (they'll be automatically filled)
   - Click "Save"

3. **Verify the Teacher Record**:
   - You should see your new teacher record in the table
   - The `id` field should be automatically generated

## Step 3: Test the Teacher Login

1. **Start your application**:
   ```bash
   pnpm dev
   ```

2. **Navigate to the Teacher Login Page**:
   - Go to `/teacher/login` in your application

3. **Log in with the Teacher Credentials**:
   - Enter the email and password you created in Step 1
   - Click "Login"

4. **Verify Access**:
   - You should be redirected to the teacher dashboard
   - You should have access to all teacher features

## Troubleshooting

### Authentication Issues

- **Error: "User not found"**: Make sure the email is exactly the same as what you entered in Supabase
- **Error: "Invalid login credentials"**: Double-check the password
- **Error: "Email not confirmed"**: In Supabase Authentication settings, you may need to disable email confirmation for testing

### Database Issues

- **Error: "Foreign key constraint violation"**: Make sure the `auth_id` in the teachers table exactly matches the User UID from Supabase Authentication
- **Error: "Permission denied"**: Check that your Row Level Security (RLS) policies are correctly set up

## Additional Notes

- For production environments, you should enable email confirmation and implement proper user management
- Consider adding additional security measures like rate limiting and multi-factor authentication
- Remember to use strong passwords for teacher accounts
