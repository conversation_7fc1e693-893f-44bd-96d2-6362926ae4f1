# Docker Deployment Guide for Science Assessment App

This guide explains how to deploy the Science Assessment App to a VPS using Docker.

## Prerequisites

- A VPS with <PERSON><PERSON> and <PERSON>er Compose installed
- SSH access to your VPS
- Git installed on your VPS

## Deployment Steps

### 1. Clone the Repository on Your VPS

```bash
# SSH into your VPS
ssh user@your-vps-ip

# Clone the repository
git clone https://github.com/your-username/science-assessment-app.git
cd science-assessment-app
```

### 2. Configure Environment Variables

The application uses environment variables for configuration. These are already set up in the `.env.production` file and will be used by Docker Compose.

### 3. Deploy with Docker Compose

Run the deployment script:

```bash
# Make the script executable if it's not already
chmod +x deploy-to-vps.sh

# Run the deployment script
./deploy-to-vps.sh
```

This script will:
- Build the Docker image
- Stop any existing containers
- Start the application in detached mode
- Show the running containers

### 4. Access the Application

Once deployed, the application will be available at:

```
http://your-vps-ip:3000
```

## Managing the Deployment

### View Logs

To view the application logs:

```bash
docker-compose logs -f
```

### Stop the Application

To stop the application:

```bash
docker-compose down
```

### Update the Application

To update the application after making changes:

```bash
# Pull the latest changes
git pull

# Run the deployment script again
./deploy-to-vps.sh
```

## Troubleshooting

### Container Not Starting

If the container fails to start, check the logs:

```bash
docker-compose logs
```

### Port Conflicts

If port 3000 is already in use, you can modify the `docker-compose.yml` file to use a different port:

```yaml
ports:
  - "8080:3000"  # Change 8080 to any available port
```

### Database Connection Issues

If the application cannot connect to Supabase, verify that the environment variables are correctly set in the `.env.production` file.
