# Vercel Deployment Guide

## 1. Create a Vercel Account

If you don't already have a Vercel account:
1. Go to https://vercel.com/signup
2. Sign up with GitHub (recommended for easier integration)

## 2. Import Your GitHub Repository

1. Once logged in to Vercel, click on "Add New..." and select "Project"
2. Connect to your GitHub account if not already connected
3. Select the "science-assessment-app" repository from the list
4. Vercel will automatically detect that it's a Next.js project

## 3. Configure Project Settings

1. Project Name: You can keep the default or customize it
2. Framework Preset: Next.js (should be auto-detected)
3. Root Directory: `./` (default)
4. Build Command: `next build` (default)
5. Output Directory: `.next` (default)

## 4. Configure Environment Variables

You need to add the following environment variables from your `.env.local` file:

| Name | Value |
|------|-------|
| NEXT_PUBLIC_SUPABASE_URL | https://nxrivucjjrzwoxgsesrp.supabase.co |
| NEXT_PUBLIC_SUPABASE_ANON_KEY | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.uM-c_xnWzCPlQlGmoU30tCKtGbvw5BcywNt9WW73h_k |
| SUPABASE_SERVICE_KEY | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im54cml2dWNqanJ6d294Z3Nlc3JwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTMzOTk4NCwiZXhwIjoyMDYwOTE1OTg0fQ.q_Psk5LOtS_IblGXwmxUeIzNYWh1VBpzN1fcBOEjAZY |
| NEXT_PUBLIC_GEMINI_API_KEY | AIzaSyDJaz6FwH3khECmc59pvAj3NVfOpH73XtI |
| NODE_ENV | production |

To add these:
1. Expand the "Environment Variables" section
2. Add each variable with its name and value
3. Click "Add" after each one

## 5. Deploy

1. Click "Deploy"
2. Vercel will build and deploy your application
3. Once complete, you'll get a URL where your app is deployed (e.g., https://science-assessment-app.vercel.app)

## 6. Custom Domain (Optional)

If you want to use a custom domain:
1. Go to your project settings in Vercel
2. Navigate to the "Domains" section
3. Add your domain and follow the instructions to configure DNS

## 7. Continuous Deployment

Vercel automatically sets up continuous deployment. Whenever you push changes to your GitHub repository, Vercel will automatically rebuild and redeploy your application.
