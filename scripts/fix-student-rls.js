const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixStudentRLS() {
  console.log('🔧 Fixing Student RLS Policies...');
  
  try {
    // Drop existing policies
    console.log('1. Dropping existing policies...');
    await supabase.rpc('exec_sql', {
      sql: `
        DROP POLICY IF EXISTS "Students can view their own data" ON public.students;
        DROP POLICY IF EXISTS "Students insertable by authenticated users" ON public.students;
        DROP POLICY IF EXISTS "Students updatable by authenticated users" ON public.students;
      `
    });

    // Create new permissive policies
    console.log('2. Creating new policies...');
    await supabase.rpc('exec_sql', {
      sql: `
        -- Allow all authenticated users to select students
        CREATE POLICY "Students accessible by all authenticated users"
          ON public.students FOR SELECT
          TO authenticated
          USING (true);

        -- Allow all authenticated users to insert students
        CREATE POLICY "Students insertable by authenticated users"
          ON public.students FOR INSERT
          TO authenticated
          WITH CHECK (true);

        -- Allow all authenticated users to update students
        CREATE POLICY "Students updatable by authenticated users"
          ON public.students FOR UPDATE
          TO authenticated
          USING (true);
      `
    });

    console.log('✅ Student RLS policies fixed successfully!');
    console.log('📝 Students can now register without teacher login.');
    
  } catch (error) {
    console.error('❌ Error fixing RLS policies:', error);
    
    // Try alternative approach using direct SQL
    console.log('🔄 Trying alternative approach...');
    try {
      const { error: error1 } = await supabase
        .from('students')
        .select('id')
        .limit(1);
      
      if (error1) {
        console.log('Current RLS is blocking access. This confirms the issue.');
      }
      
      console.log('💡 Manual fix required. Please run these SQL commands in Supabase SQL Editor:');
      console.log(`
-- Drop existing policies
DROP POLICY IF EXISTS "Students can view their own data" ON public.students;
DROP POLICY IF EXISTS "Students insertable by authenticated users" ON public.students;
DROP POLICY IF EXISTS "Students updatable by authenticated users" ON public.students;

-- Create new policies
CREATE POLICY "Students accessible by all authenticated users"
  ON public.students FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Students insertable by authenticated users"
  ON public.students FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Students updatable by authenticated users"
  ON public.students FOR UPDATE
  TO authenticated
  USING (true);
      `);
    } catch (altError) {
      console.error('Alternative approach also failed:', altError);
    }
  }
}

fixStudentRLS();
