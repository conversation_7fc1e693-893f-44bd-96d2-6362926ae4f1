const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

const preTestQuestions = [
  {
    text: "What is the primary source of energy for most living organisms?",
    options: [
      "Sunlight",
      "Wind",
      "Water",
      "Electricity"
    ],
    correct_answer: "Sunlight",
    explanation: "Sunlight is the primary source of energy for most living organisms through photosynthesis."
  },
  {
    text: "How do plants convert sunlight into chemical energy?",
    options: [
      "Respiration",
      "Photosynthesis",
      "Fermentation",
      "Combustion"
    ],
    correct_answer: "Photosynthesis",
    explanation: "Plants use photosynthesis to convert sunlight into chemical energy stored in glucose."
  }
];

const translatedQuestions = [
  {
    text: "Apakah sumber energi utama bagi sebagian besar makhluk hidup?",
    options: [
      "<PERSON><PERSON>aya Matahari",
      "<PERSON><PERSON>",
      "<PERSON>",
      "<PERSON><PERSON>"
    ],
    correct_answer: "Cahaya Matahari",
    explanation: "Cahaya matahari adalah sumber energi utama bagi sebagian besar makhluk hidup melalui proses fotosintesis."
  },
  {
    text: "Bagaimana tumbuhan mengubah cahaya matahari menjadi energi kimia?",
    options: [
      "Respirasi",
      "Fotosintesis",
      "Fermentasi",
      "Pembakaran"
    ],
    correct_answer: "Fotosintesis",
    explanation: "Tumbuhan menggunakan fotosintesis untuk mengubah cahaya matahari menjadi energi kimia yang tersimpan dalam glukosa."
  }
];

async function updatePreTestQuestions() {
  for (let i = 0; i < translatedQuestions.length; i++) {
    const { data, error } = await supabase
      .from('questions')
      .update({
        text: translatedQuestions[i].text,
        options: translatedQuestions[i].options,
        correct_answer: translatedQuestions[i].correct_answer,
        explanation: translatedQuestions[i].explanation
      })
      .eq('question_type', 'pre-test')
      .eq('text', preTestQuestions[i].text);

    if (error) {
      console.error(`Error updating question ${i + 1}:`, error);
    } else {
      console.log(`Question ${i + 1} updated successfully`);
    }
  }
}

updatePreTestQuestions();
