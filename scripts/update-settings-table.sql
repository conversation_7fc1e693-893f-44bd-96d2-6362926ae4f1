-- Check if settings table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'settings'
  ) THEN
    -- Create settings table if it doesn't exist
    CREATE TABLE settings (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      kkm INTEGER NOT NULL DEFAULT 70,
      pre_test_questions INTEGER NOT NULL DEFAULT 5,
      main_test_questions INTEGER NOT NULL DEFAULT 20,
      enable_ai BOOLEAN NOT NULL DEFAULT true,
      ai_model TEXT NOT NULL DEFAULT 'gemini',
      ai_prompt TEXT NOT NULL DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE,
      school_name TEXT DEFAULT 'Science Academy',
      school_logo_url TEXT,
      theme_color TEXT DEFAULT '#4F46E5',
      enable_student_registration BOOLEAN DEFAULT true
    );
  END IF;
END $$;

-- Insert default settings if no records exist
INSERT INTO settings (
  kkm,
  pre_test_questions,
  main_test_questions,
  enable_ai,
  ai_model,
  ai_prompt,
  school_name,
  theme_color,
  enable_student_registration
)
SELECT
  70,
  5,
  10,
  true,
  'gemini',
  'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
  'Science Academy',
  '#4F46E5',
  true
WHERE NOT EXISTS (SELECT 1 FROM settings);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at timestamp
DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;
CREATE TRIGGER update_settings_updated_at
BEFORE UPDATE ON settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
