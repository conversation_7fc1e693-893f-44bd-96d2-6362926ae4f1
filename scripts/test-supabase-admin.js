const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Environment Variables Check:');
console.log('SUPABASE_URL:', supabaseUrl ? '✅ Present' : '❌ Missing');
console.log('SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Present' : '❌ Missing');
console.log('SUPABASE_SERVICE_KEY:', supabaseServiceKey ? '✅ Present' : '❌ Missing');

// Create clients like in the app
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : supabase;

async function testSupabaseAdmin() {
  console.log('\n🧪 Testing Supabase Admin Access...');
  
  try {
    // Test 1: Regular client (should fail due to RLS)
    console.log('1. Testing regular client (should fail):');
    const { data: regularData, error: regularError } = await supabase
      .from('students')
      .select('*')
      .limit(1);
    
    if (regularError) {
      console.log('❌ Regular client failed (expected):', regularError.message);
    } else {
      console.log('✅ Regular client worked (unexpected):', regularData?.length || 0, 'records');
    }

    // Test 2: Admin client (should work)
    console.log('2. Testing admin client (should work):');
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('students')
      .select('*')
      .limit(1);
    
    if (adminError) {
      console.log('❌ Admin client failed:', adminError.message);
      console.log('🔍 This means SUPABASE_SERVICE_KEY is invalid or RLS is blocking');
    } else {
      console.log('✅ Admin client worked:', adminData?.length || 0, 'records');
    }

    // Test 3: Try to create a student with admin client
    console.log('3. Testing student creation with admin client:');
    const testStudentId = `TEST_${Date.now()}`;
    const { data: newStudent, error: createError } = await supabaseAdmin
      .from('students')
      .insert({
        name: 'Test Student Admin',
        student_id: testStudentId,
        current_level: 'easy'
      })
      .select();

    if (createError) {
      console.log('❌ Cannot create student with admin:', createError.message);
    } else {
      console.log('✅ Created student with admin:', newStudent[0]);
      
      // Clean up
      await supabaseAdmin
        .from('students')
        .delete()
        .eq('student_id', testStudentId);
      console.log('🧹 Cleaned up test student');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSupabaseAdmin();
