// This script can be used to create a teacher account in Supabase Auth
// Run with: node scripts/setup-auth.js

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const readline = require('readline');

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  console.error('Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY defined.');
  console.error('You can find these in your Supabase dashboard under Settings > API.');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function createTeacherAccount() {
  return new Promise((resolve) => {
    rl.question('Enter teacher email: ', (email) => {
      rl.question('Enter teacher name: ', (name) => {
        rl.question('Enter teacher password (min 6 characters): ', async (password) => {
          try {
            console.log(`Creating teacher account for ${email}...`);

            // Create user with Supabase Auth
            const { data, error } = await supabase.auth.admin.createUser({
              email,
              password,
              email_confirm: true // Auto-confirm the email
            });

            if (error) {
              console.error('Error creating teacher account:', error.message);
              resolve();
              return;
            }

            console.log('Teacher auth account created successfully!');
            console.log('User ID:', data.user.id);

            // Now create an entry in the teachers table
            console.log('Creating teacher record in database...');
            const { data: teacherData, error: teacherError } = await supabase
              .from('teachers')
              .insert([
                {
                  auth_id: data.user.id,
                  name: name || email.split('@')[0], // Use name or extract from email
                  email: email
                }
              ])
              .select();

            if (teacherError) {
              console.error('Error creating teacher record:', teacherError.message);
            } else {
              console.log('Teacher record created successfully!');
              console.log('Teacher ID:', teacherData[0].id);
            }

            resolve();
          } catch (error) {
            console.error('Error:', error);
            resolve();
          }
        });
      });
    });
  });
}

async function main() {
  try {
    await createTeacherAccount();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    rl.close();
  }
}

main();
