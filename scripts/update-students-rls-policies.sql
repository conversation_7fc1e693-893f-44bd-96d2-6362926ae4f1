-- Update Row Level Security (RLS) policies for the students table

-- First, let's check if RLS is enabled on the students table
SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = 'students';

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Students accessible by all authenticated users" ON public.students;
DROP POLICY IF EXISTS "Students insertable by authenticated users" ON public.students;
DROP POLICY IF EXISTS "Students updatable by authenticated users" ON public.students;
DROP POLICY IF EXISTS "Students deletable by authenticated users" ON public.students;

-- Create policies for the students table

-- 1. Allow authenticated users to select (read) students
CREATE POLICY "Students accessible by all authenticated users"
  ON public.students FOR SELECT
  TO authenticated
  USING (true);

-- 2. Allow authenticated users to insert students
CREATE POLICY "Students insertable by authenticated users"
  ON public.students FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- 3. Allow authenticated users to update students
CREATE POLICY "Students updatable by authenticated users"
  ON public.students FOR UPDATE
  TO authenticated
  USING (true);

-- 4. Allow authenticated users to delete students
CREATE POLICY "Students deletable by authenticated users"
  ON public.students FOR DELETE
  TO authenticated
  USING (true);

-- Make sure RLS is enabled on the students table
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;

-- Verify the policies
SELECT * FROM pg_policies WHERE tablename = 'students';
