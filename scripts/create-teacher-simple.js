const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTeacher() {
  console.log('🔧 Creating Teacher Account...\n');
  
  // Default teacher credentials
  const teacherEmail = '<EMAIL>';
  const teacherPassword = 'password123';
  const teacherName = 'Teacher';

  try {
    console.log('1. Creating authentication user...');
    
    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: teacherEmail,
      password: teacherPassword,
      email_confirm: true // Auto-confirm email
    });

    if (authError) {
      if (authError.message.includes('User already registered')) {
        console.log('⚠️  User already exists in auth, trying to get user info...');
        
        // Try to get existing user
        const { data: users } = await supabase.auth.admin.listUsers();
        const existingUser = users.users.find(u => u.email === teacherEmail);
        
        if (existingUser) {
          console.log('✅ Found existing user:', existingUser.id);
          
          // Check if teacher record exists
          const { data: existingTeacher } = await supabase
            .from('teachers')
            .select('*')
            .eq('email', teacherEmail)
            .single();
          
          if (existingTeacher) {
            console.log('✅ Teacher record already exists');
            console.log('📧 Email:', teacherEmail);
            console.log('🔑 Password:', teacherPassword);
            console.log('🎉 Teacher account is ready to use!');
            return;
          } else {
            // Create teacher record for existing auth user
            console.log('2. Creating teacher record for existing user...');
            const { data: teacherData, error: teacherError } = await supabase
              .from('teachers')
              .insert({
                auth_id: existingUser.id,
                name: teacherName,
                email: teacherEmail
              })
              .select();

            if (teacherError) {
              console.error('❌ Error creating teacher record:', teacherError.message);
              return;
            }

            console.log('✅ Teacher record created successfully!');
            console.log('📧 Email:', teacherEmail);
            console.log('🔑 Password:', teacherPassword);
            console.log('🎉 Teacher account is ready to use!');
            return;
          }
        }
      } else {
        console.error('❌ Error creating auth user:', authError.message);
        return;
      }
    }

    console.log('✅ Auth user created successfully!');
    console.log('👤 User ID:', authData.user.id);

    console.log('2. Creating teacher record in database...');
    
    // Create teacher record
    const { data: teacherData, error: teacherError } = await supabase
      .from('teachers')
      .insert({
        auth_id: authData.user.id,
        name: teacherName,
        email: teacherEmail
      })
      .select();

    if (teacherError) {
      console.error('❌ Error creating teacher record:', teacherError.message);
      console.log('💡 You may need to create the teacher record manually:');
      console.log(`   - Go to Supabase Dashboard > Table Editor > teachers`);
      console.log(`   - Insert: auth_id="${authData.user.id}", name="${teacherName}", email="${teacherEmail}"`);
      return;
    }

    console.log('✅ Teacher record created successfully!');
    console.log('🎉 Teacher account setup complete!\n');
    
    console.log('📋 Login Credentials:');
    console.log('📧 Email:', teacherEmail);
    console.log('🔑 Password:', teacherPassword);
    console.log('🔗 Login URL: http://localhost:3000/teacher/login');
    
    console.log('\n💡 You can now login with these credentials!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    
    console.log('\n🛠️  Manual Setup Instructions:');
    console.log('1. Go to Supabase Dashboard > Authentication > Users');
    console.log('2. Click "Add User"');
    console.log(`3. Email: ${teacherEmail}`);
    console.log(`4. Password: ${teacherPassword}`);
    console.log('5. Go to Table Editor > teachers');
    console.log('6. Click "Insert row"');
    console.log('7. Fill in: auth_id (from step 4), name, email');
  }
}

createTeacher();
