-- Create a function to update settings directly via SQL
CREATE OR <PERSON>EP<PERSON>CE FUNCTION update_settings_direct(
  settings_id UUID,
  school_name_val TEXT,
  teacher_name_val TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  UPDATE settings
  SET 
    school_name = school_name_val,
    teacher_name = teacher_name_val,
    updated_at = NOW()
  WHERE id = settings_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;
