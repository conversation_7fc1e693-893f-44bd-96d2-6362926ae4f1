-- Simple setup script for settings table

-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  kkm INTEGER NOT NULL DEFAULT 70,
  pre_test_questions INTEGER NOT NULL DEFAULT 5,
  main_test_questions INTEGER NOT NULL DEFAULT 10,
  enable_ai BOOLEAN NOT NULL DEFAULT true
);

-- Add columns if they don't exist
ALTER TABLE settings ADD COLUMN IF NOT EXISTS ai_model TEXT DEFAULT 'gemini';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS school_name TEXT DEFAULT 'Science Academy';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS school_logo_url TEXT;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS theme_color TEXT DEFAULT '#4F46E5';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS enable_student_registration BOOLEAN DEFAULT true;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS teacher_name TEXT DEFAULT 'Ms. Johnson';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS grade_level TEXT DEFAULT '5';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS subject TEXT DEFAULT 'science';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS topic TEXT DEFAULT 'Energy Transformation';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS academic_year TEXT DEFAULT '2023-2024';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS auto_grade BOOLEAN DEFAULT true;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS show_explanation BOOLEAN DEFAULT true;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS allow_retry BOOLEAN DEFAULT true;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS notify_teacher BOOLEAN DEFAULT true;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS notify_parent BOOLEAN DEFAULT false;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS theme TEXT DEFAULT 'default';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE settings ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE;

-- Insert default settings if the table is empty
INSERT INTO settings (
  kkm,
  pre_test_questions,
  main_test_questions,
  enable_ai,
  ai_model,
  ai_prompt,
  school_name,
  theme_color,
  enable_student_registration,
  teacher_name,
  grade_level,
  subject,
  topic,
  academic_year,
  auto_grade,
  show_explanation,
  allow_retry,
  max_retries,
  difficulty_distribution,
  notify_teacher,
  notify_parent,
  theme,
  email_template
)
SELECT
  70,
  5,
  10,
  true,
  'gemini',
  'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
  'Science Academy',
  '#4F46E5',
  true,
  'Ms. Johnson',
  '5',
  'science',
  'Energy Transformation',
  '2023-2024',
  true,
  true,
  true,
  3,
  '{"easy": 30, "medium": 40, "hard": 30}'::jsonb,
  true,
  false,
  'default',
  'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]'
WHERE NOT EXISTS (SELECT 1 FROM settings);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at timestamp
DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;
CREATE TRIGGER update_settings_updated_at
BEFORE UPDATE ON settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to update settings directly via SQL
CREATE OR REPLACE FUNCTION update_settings_direct(
  settings_id UUID,
  school_name_val TEXT,
  teacher_name_val TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  UPDATE settings
  SET
    school_name = school_name_val,
    teacher_name = teacher_name_val,
    updated_at = NOW()
  WHERE id = settings_id;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Verify settings
SELECT * FROM settings LIMIT 1;
