// Create settings table manually
require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// Get database connection details from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Extract database connection details from Supabase URL
// Format: https://[project-ref].supabase.co
const projectRef = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)[1];

// Create a connection pool
const pool = new Pool({
  host: `db.${projectRef}.supabase.co`,
  database: 'postgres',
  user: 'postgres',
  password: supabase<PERSON><PERSON>,
  port: 5432,
  ssl: {
    rejectUnauthorized: false
  }
});

async function createSettingsTable() {
  const client = await pool.connect();
  
  try {
    console.log('Creating settings table...');
    
    // Create settings table
    await client.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        kkm INTEGER NOT NULL DEFAULT 70,
        pre_test_questions INTEGER NOT NULL DEFAULT 5,
        main_test_questions INTEGER NOT NULL DEFAULT 10,
        enable_ai BOOLEAN NOT NULL DEFAULT true,
        ai_model TEXT NOT NULL DEFAULT 'gemini',
        ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
        school_name TEXT DEFAULT 'Science Academy',
        school_logo_url TEXT,
        theme_color TEXT DEFAULT '#4F46E5',
        enable_student_registration BOOLEAN DEFAULT true,
        teacher_name TEXT DEFAULT 'Ms. Johnson',
        grade_level TEXT DEFAULT '5',
        subject TEXT DEFAULT 'science',
        topic TEXT DEFAULT 'Energy Transformation',
        academic_year TEXT DEFAULT '2023-2024',
        auto_grade BOOLEAN DEFAULT true,
        show_explanation BOOLEAN DEFAULT true,
        allow_retry BOOLEAN DEFAULT true,
        max_retries INTEGER DEFAULT 3,
        difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}',
        notify_teacher BOOLEAN DEFAULT true,
        notify_parent BOOLEAN DEFAULT false,
        theme TEXT DEFAULT 'default',
        email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE
      );
    `);
    
    console.log('Settings table created successfully.');
    
    // Check if settings table has any records
    const countResult = await client.query('SELECT COUNT(*) FROM settings;');
    const count = parseInt(countResult.rows[0].count);
    
    if (count === 0) {
      console.log('Settings table is empty. Inserting default settings...');
      
      // Insert default settings
      await client.query(`
        INSERT INTO settings (
          kkm,
          pre_test_questions,
          main_test_questions,
          enable_ai,
          ai_model,
          ai_prompt,
          school_name,
          theme_color,
          enable_student_registration,
          teacher_name,
          grade_level,
          subject,
          topic,
          academic_year,
          auto_grade,
          show_explanation,
          allow_retry,
          max_retries,
          difficulty_distribution,
          notify_teacher,
          notify_parent,
          theme,
          email_template
        ) VALUES (
          70,
          5,
          10,
          true,
          'gemini',
          'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          'Science Academy',
          '#4F46E5',
          true,
          'Ms. Johnson',
          '5',
          'science',
          'Energy Transformation',
          '2023-2024',
          true,
          true,
          true,
          3,
          '{"easy": 30, "medium": 40, "hard": 30}'::jsonb,
          true,
          false,
          'default',
          'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]'
        );
      `);
      
      console.log('Default settings inserted successfully.');
    } else {
      console.log(`Settings table already has ${count} record(s).`);
      
      // Update existing settings with default values for new columns
      await client.query(`
        UPDATE settings
        SET
          teacher_name = COALESCE(teacher_name, 'Ms. Johnson'),
          grade_level = COALESCE(grade_level, '5'),
          subject = COALESCE(subject, 'science'),
          topic = COALESCE(topic, 'Energy Transformation'),
          academic_year = COALESCE(academic_year, '2023-2024'),
          auto_grade = COALESCE(auto_grade, true),
          show_explanation = COALESCE(show_explanation, true),
          allow_retry = COALESCE(allow_retry, true),
          max_retries = COALESCE(max_retries, 3),
          difficulty_distribution = COALESCE(difficulty_distribution, '{"easy": 30, "medium": 40, "hard": 30}'::jsonb),
          notify_teacher = COALESCE(notify_teacher, true),
          notify_parent = COALESCE(notify_parent, false),
          theme = COALESCE(theme, 'default'),
          email_template = COALESCE(email_template, 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]'),
          updated_at = NOW()
        WHERE id IS NOT NULL;
      `);
      
      console.log('Settings updated with default values for new columns.');
    }
    
    // Create function to update the updated_at timestamp
    await client.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // Create trigger to update the updated_at timestamp
    await client.query(`
      DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;
      CREATE TRIGGER update_settings_updated_at
      BEFORE UPDATE ON settings
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `);
    
    console.log('Created trigger for updating the updated_at timestamp.');
    
    // Verify settings
    const settingsResult = await client.query('SELECT * FROM settings LIMIT 1;');
    console.log('Current settings:', settingsResult.rows[0] || 'No settings found');
    
    console.log('Settings table setup complete.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createSettingsTable()
  .then(() => {
    console.log('Setup process completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in setup process:', error);
    process.exit(1);
  });
