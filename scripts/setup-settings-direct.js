// Setup settings table with direct SQL commands
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupSettingsTable() {
  try {
    console.log('Setting up settings table...');
    
    // Create settings table if it doesn't exist
    const { error: createTableError } = await supabase.sql`
      CREATE TABLE IF NOT EXISTS settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        kkm INTEGER NOT NULL DEFAULT 70,
        pre_test_questions INTEGER NOT NULL DEFAULT 5,
        main_test_questions INTEGER NOT NULL DEFAULT 10,
        enable_ai BOOLEAN NOT NULL DEFAULT true,
        ai_model TEXT NOT NULL DEFAULT 'gemini',
        ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
        school_name TEXT DEFAULT 'Science Academy',
        school_logo_url TEXT,
        theme_color TEXT DEFAULT '#4F46E5',
        enable_student_registration BOOLEAN DEFAULT true,
        teacher_name TEXT DEFAULT 'Ms. Johnson',
        grade_level TEXT DEFAULT '5',
        subject TEXT DEFAULT 'science',
        topic TEXT DEFAULT 'Energy Transformation',
        academic_year TEXT DEFAULT '2023-2024',
        auto_grade BOOLEAN DEFAULT true,
        show_explanation BOOLEAN DEFAULT true,
        allow_retry BOOLEAN DEFAULT true,
        max_retries INTEGER DEFAULT 3,
        difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}',
        notify_teacher BOOLEAN DEFAULT true,
        notify_parent BOOLEAN DEFAULT false,
        theme TEXT DEFAULT 'default',
        email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE
      );
    `;
    
    if (createTableError) {
      console.error('Error creating settings table:', createTableError);
      return;
    }
    
    console.log('Settings table created or already exists.');
    
    // Check if settings table has any records
    const { data: countResult, error: countError } = await supabase.sql`
      SELECT COUNT(*) FROM settings;
    `;
    
    if (countError) {
      console.error('Error checking settings count:', countError);
      return;
    }
    
    const count = parseInt(countResult.data[0].count);
    
    if (count === 0) {
      console.log('Settings table is empty. Inserting default settings...');
      
      // Insert default settings
      const { error: insertError } = await supabase.sql`
        INSERT INTO settings (
          kkm,
          pre_test_questions,
          main_test_questions,
          enable_ai,
          ai_model,
          ai_prompt,
          school_name,
          theme_color,
          enable_student_registration,
          teacher_name,
          grade_level,
          subject,
          topic,
          academic_year,
          auto_grade,
          show_explanation,
          allow_retry,
          max_retries,
          difficulty_distribution,
          notify_teacher,
          notify_parent,
          theme,
          email_template
        ) VALUES (
          70,
          5,
          10,
          true,
          'gemini',
          'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          'Science Academy',
          '#4F46E5',
          true,
          'Ms. Johnson',
          '5',
          'science',
          'Energy Transformation',
          '2023-2024',
          true,
          true,
          true,
          3,
          '{"easy": 30, "medium": 40, "hard": 30}'::jsonb,
          true,
          false,
          'default',
          'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]'
        );
      `;
      
      if (insertError) {
        console.error('Error inserting default settings:', insertError);
        return;
      }
      
      console.log('Default settings inserted successfully.');
    } else {
      console.log(`Settings table already has ${count} record(s).`);
    }
    
    // Verify settings
    const { data: settingsResult, error: verifyError } = await supabase.sql`
      SELECT * FROM settings LIMIT 1;
    `;
    
    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }
    
    console.log('Current settings:', settingsResult.data[0] || 'No settings found');
    console.log('Settings table setup complete.');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

setupSettingsTable()
  .then(() => {
    console.log('Settings table setup complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in setup process:', error);
    process.exit(1);
  });
