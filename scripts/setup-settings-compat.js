// Setup settings table with compatible Supabase methods
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupSettingsTable() {
  try {
    console.log('Setting up settings table...');
    
    // Check if settings table exists
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('check_table_exists', { table_name: 'settings' });
    
    if (tableError) {
      console.error('Error checking if settings table exists:', tableError);
      // Try to create the function if it doesn't exist
      await supabase.rpc('create_check_table_exists_function');
      
      // Try again
      const { data: retryTableInfo, error: retryTableError } = await supabase
        .rpc('check_table_exists', { table_name: 'settings' });
      
      if (retryTableError) {
        console.error('Error checking if settings table exists (retry):', retryTableError);
        
        // Create the function manually
        const { error: createFunctionError } = await supabase
          .from('_exec_sql')
          .insert({
            query: `
              CREATE OR REPLACE FUNCTION check_table_exists(table_name text)
              RETURNS boolean
              LANGUAGE plpgsql
              AS $$
              DECLARE
                table_exists boolean;
              BEGIN
                SELECT EXISTS (
                  SELECT FROM information_schema.tables 
                  WHERE table_schema = 'public' 
                  AND table_name = $1
                ) INTO table_exists;
                RETURN table_exists;
              END;
              $$;
            `
          });
        
        if (createFunctionError) {
          console.error('Error creating check_table_exists function:', createFunctionError);
          
          // Last resort: just try to create the table
          console.log('Attempting to create settings table directly...');
          await createSettingsTable();
          return;
        }
        
        // Try one more time
        const { data: finalTableInfo, error: finalTableError } = await supabase
          .rpc('check_table_exists', { table_name: 'settings' });
        
        if (finalTableError) {
          console.error('Error checking if settings table exists (final retry):', finalTableError);
          // Just try to create the table
          await createSettingsTable();
          return;
        }
        
        if (!finalTableInfo) {
          await createSettingsTable();
          return;
        }
      } else {
        if (!retryTableInfo) {
          await createSettingsTable();
          return;
        }
      }
    } else {
      if (!tableInfo) {
        await createSettingsTable();
        return;
      }
    }
    
    // Check if settings table has any records
    const { data: settingsData, error: countError } = await supabase
      .from('settings')
      .select('count', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error checking settings count:', countError);
      return;
    }
    
    const count = settingsData;
    
    if (!count || count === 0) {
      console.log('Settings table is empty. Inserting default settings...');
      await insertDefaultSettings();
    } else {
      console.log(`Settings table already has records. Checking for missing columns...`);
      await updateSettingsColumns();
    }
    
    // Verify settings
    const { data: settings, error: verifyError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }
    
    console.log('Current settings:', settings[0] || 'No settings found');
    console.log('Settings table setup complete.');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function createSettingsTable() {
  console.log('Creating settings table...');
  
  // Create settings table
  const { error: createError } = await supabase
    .from('_exec_sql')
    .insert({
      query: `
        CREATE TABLE IF NOT EXISTS settings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          kkm INTEGER NOT NULL DEFAULT 70,
          pre_test_questions INTEGER NOT NULL DEFAULT 5,
          main_test_questions INTEGER NOT NULL DEFAULT 10,
          enable_ai BOOLEAN NOT NULL DEFAULT true,
          ai_model TEXT NOT NULL DEFAULT 'gemini',
          ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name TEXT DEFAULT 'Science Academy',
          school_logo_url TEXT,
          theme_color TEXT DEFAULT '#4F46E5',
          enable_student_registration BOOLEAN DEFAULT true,
          teacher_name TEXT DEFAULT 'Ms. Johnson',
          grade_level TEXT DEFAULT '5',
          subject TEXT DEFAULT 'science',
          topic TEXT DEFAULT 'Energy Transformation',
          academic_year TEXT DEFAULT '2023-2024',
          auto_grade BOOLEAN DEFAULT true,
          show_explanation BOOLEAN DEFAULT true,
          allow_retry BOOLEAN DEFAULT true,
          max_retries INTEGER DEFAULT 3,
          difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}',
          notify_teacher BOOLEAN DEFAULT true,
          notify_parent BOOLEAN DEFAULT false,
          theme TEXT DEFAULT 'default',
          email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE
        );
      `
    });
  
  if (createError) {
    console.error('Error creating settings table:', createError);
    return false;
  }
  
  console.log('Settings table created successfully.');
  return true;
}

async function insertDefaultSettings() {
  // Insert default settings
  const { error: insertError } = await supabase
    .from('settings')
    .insert({
      kkm: 70,
      pre_test_questions: 5,
      main_test_questions: 10,
      enable_ai: true,
      ai_model: 'gemini',
      ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
      school_name: 'Science Academy',
      theme_color: '#4F46E5',
      enable_student_registration: true,
      teacher_name: 'Ms. Johnson',
      grade_level: '5',
      subject: 'science',
      topic: 'Energy Transformation',
      academic_year: '2023-2024',
      auto_grade: true,
      show_explanation: true,
      allow_retry: true,
      max_retries: 3,
      difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
      notify_teacher: true,
      notify_parent: false,
      theme: 'default',
      email_template: `Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]`
    });
  
  if (insertError) {
    console.error('Error inserting default settings:', insertError);
    return false;
  }
  
  console.log('Default settings inserted successfully.');
  return true;
}

async function updateSettingsColumns() {
  // List of columns to check and add if missing
  const columns = [
    { name: 'teacher_name', type: 'TEXT', default: "'Ms. Johnson'" },
    { name: 'grade_level', type: 'TEXT', default: "'5'" },
    { name: 'subject', type: 'TEXT', default: "'science'" },
    { name: 'topic', type: 'TEXT', default: "'Energy Transformation'" },
    { name: 'academic_year', type: 'TEXT', default: "'2023-2024'" },
    { name: 'auto_grade', type: 'BOOLEAN', default: 'true' },
    { name: 'show_explanation', type: 'BOOLEAN', default: 'true' },
    { name: 'allow_retry', type: 'BOOLEAN', default: 'true' },
    { name: 'max_retries', type: 'INTEGER', default: '3' },
    { name: 'difficulty_distribution', type: 'JSONB', default: "'{\"easy\": 30, \"medium\": 40, \"hard\": 30}'" },
    { name: 'notify_teacher', type: 'BOOLEAN', default: 'true' },
    { name: 'notify_parent', type: 'BOOLEAN', default: 'false' },
    { name: 'theme', type: 'TEXT', default: "'default'" },
    { name: 'email_template', type: 'TEXT', default: "'Dear [RECIPIENT_NAME],\\n\\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\\n\\n[PASS_FAIL_MESSAGE]\\n\\nYou can view the detailed results by logging into the assessment platform.\\n\\nBest regards,\\n[TEACHER_NAME]\\n[SCHOOL_NAME]'" }
  ];
  
  // Check each column and add if missing
  for (const column of columns) {
    const { error: checkColumnError } = await supabase
      .from('_exec_sql')
      .insert({
        query: `
          DO $$
          BEGIN
            IF NOT EXISTS (
              SELECT FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'settings' 
              AND column_name = '${column.name}'
            ) THEN
              ALTER TABLE settings ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default};
              RAISE NOTICE 'Added column ${column.name}';
            ELSE
              RAISE NOTICE 'Column ${column.name} already exists';
            END IF;
          END $$;
        `
      });
    
    if (checkColumnError) {
      console.error(`Error checking/adding column ${column.name}:`, checkColumnError);
    } else {
      console.log(`Checked/added column ${column.name}`);
    }
  }
  
  // Update existing settings with default values for new columns
  const { error: updateError } = await supabase
    .from('_exec_sql')
    .insert({
      query: `
        UPDATE settings
        SET
          teacher_name = COALESCE(teacher_name, 'Ms. Johnson'),
          grade_level = COALESCE(grade_level, '5'),
          subject = COALESCE(subject, 'science'),
          topic = COALESCE(topic, 'Energy Transformation'),
          academic_year = COALESCE(academic_year, '2023-2024'),
          auto_grade = COALESCE(auto_grade, true),
          show_explanation = COALESCE(show_explanation, true),
          allow_retry = COALESCE(allow_retry, true),
          max_retries = COALESCE(max_retries, 3),
          difficulty_distribution = COALESCE(difficulty_distribution, '{"easy": 30, "medium": 40, "hard": 30}'::jsonb),
          notify_teacher = COALESCE(notify_teacher, true),
          notify_parent = COALESCE(notify_parent, false),
          theme = COALESCE(theme, 'default'),
          email_template = COALESCE(email_template, 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]'),
          updated_at = NOW()
        WHERE id IS NOT NULL;
      `
    });
  
  if (updateError) {
    console.error('Error updating settings with default values:', updateError);
    return false;
  }
  
  console.log('Settings updated with default values for new columns.');
  return true;
}

// Create the _exec_sql function if it doesn't exist
async function createExecSqlFunction() {
  const { error } = await supabase
    .rpc('create_exec_sql_function');
  
  if (error) {
    console.error('Error creating _exec_sql function:', error);
    
    // Try to create it directly
    const { error: directError } = await supabase
      .from('_exec_sql')
      .insert({
        query: `
          CREATE OR REPLACE FUNCTION exec_sql(query text)
          RETURNS void
          LANGUAGE plpgsql
          AS $$
          BEGIN
            EXECUTE query;
          END;
          $$;
          
          CREATE TABLE IF NOT EXISTS _exec_sql (
            id SERIAL PRIMARY KEY,
            query TEXT NOT NULL,
            executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          
          CREATE OR REPLACE FUNCTION _exec_sql_trigger_function()
          RETURNS TRIGGER
          LANGUAGE plpgsql
          AS $$
          BEGIN
            EXECUTE NEW.query;
            RETURN NEW;
          END;
          $$;
          
          DROP TRIGGER IF EXISTS _exec_sql_trigger ON _exec_sql;
          CREATE TRIGGER _exec_sql_trigger
          BEFORE INSERT ON _exec_sql
          FOR EACH ROW
          EXECUTE FUNCTION _exec_sql_trigger_function();
        `
      });
    
    if (directError) {
      console.error('Error creating _exec_sql function directly:', directError);
      return false;
    }
  }
  
  return true;
}

// Main function
async function main() {
  // First, create the _exec_sql function
  await createExecSqlFunction();
  
  // Then setup the settings table
  await setupSettingsTable();
}

main()
  .then(() => {
    console.log('Settings table setup complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in setup process:', error);
    process.exit(1);
  });
