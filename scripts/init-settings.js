// Initialize settings table
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function initSettings() {
  try {
    // Create settings table if it doesn't exist
    const { error: createError } = await supabase.rpc('create_settings_table_if_not_exists');
    
    if (createError) {
      console.error('Error creating settings table:', createError);
      
      // Try to execute the SQL directly if the RPC function doesn't exist
      const { error: sqlError } = await supabase.sql`
        CREATE TABLE IF NOT EXISTS settings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          key TEXT NOT NULL UNIQUE,
          value JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      if (sqlError) {
        console.error('Error creating settings table with SQL:', sqlError);
        return;
      }
    }
    
    // Insert default settings if they don't exist
    const { error: insertError } = await supabase
      .from('settings')
      .insert({
        key: 'app_settings',
        value: {
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_provider: 'gemini'
        }
      })
      .onConflict('key')
      .ignore();
    
    if (insertError) {
      console.error('Error inserting default settings:', insertError);
      return;
    }
    
    console.log('Settings table initialized successfully!');
    
    // Verify settings
    const { data, error: fetchError } = await supabase
      .from('settings')
      .select('*')
      .eq('key', 'app_settings')
      .single();
    
    if (fetchError) {
      console.error('Error fetching settings:', fetchError);
      return;
    }
    
    console.log('Current settings:', data.value);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

initSettings()
  .then(() => {
    console.log('Settings initialization complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in initialization process:', error);
    process.exit(1);
  });
