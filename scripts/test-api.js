// Test the API functions directly
const { createStudent, getStudentByStudentId } = require('../lib/api.ts');

async function testAPI() {
  console.log('🧪 Testing API Functions...');
  
  try {
    const testStudentId = `TEST_${Date.now()}`;
    
    console.log('1. Testing createStudent...');
    const newStudent = await createStudent({
      name: 'Test Student API',
      student_id: testStudentId,
      current_level: 'easy'
    });
    
    if (newStudent) {
      console.log('✅ createStudent works:', newStudent);
      
      console.log('2. Testing getStudentByStudentId...');
      const foundStudent = await getStudentByStudentId(testStudentId);
      
      if (foundStudent) {
        console.log('✅ getStudentByStudentId works:', foundStudent);
      } else {
        console.log('❌ getStudentByStudentId failed');
      }
    } else {
      console.log('❌ createStudent failed');
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

testAPI();
