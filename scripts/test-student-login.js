const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Use regular client (not admin) to test RLS policies
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testStudentLogin() {
  console.log('🧪 Testing Student Login Functionality...');
  
  try {
    // Test 1: Try to read students table (should work now)
    console.log('1. Testing student table access...');
    const { data: students, error: readError } = await supabase
      .from('students')
      .select('*')
      .limit(5);
    
    if (readError) {
      console.log('❌ Cannot read students table:', readError.message);
      console.log('🔍 This suggests <PERSON><PERSON> is still blocking access');
    } else {
      console.log('✅ Can read students table');
      console.log(`📊 Found ${students.length} existing students`);
    }

    // Test 2: Try to create a test student (should work now)
    console.log('2. Testing student creation...');
    const testStudentId = `TEST_${Date.now()}`;
    const { data: newStudent, error: createError } = await supabase
      .from('students')
      .insert({
        name: 'Test Student',
        student_id: testStudentId,
        current_level: 'easy'
      })
      .select();

    if (createError) {
      console.log('❌ Cannot create student:', createError.message);
      console.log('🔍 This suggests INSERT policy is still missing');
    } else {
      console.log('✅ Can create students');
      console.log('👤 Created test student:', newStudent[0]);
      
      // Clean up test student
      await supabase
        .from('students')
        .delete()
        .eq('student_id', testStudentId);
      console.log('🧹 Cleaned up test student');
    }

    // Test 3: Check current RLS policies
    console.log('3. Checking current RLS policies...');
    const { data: policies, error: policyError } = await supabase
      .rpc('exec_sql', {
        sql: "SELECT * FROM pg_policies WHERE tablename = 'students';"
      });

    if (!policyError && policies) {
      console.log('📋 Current RLS policies for students table:');
      policies.forEach(policy => {
        console.log(`  - ${policy.policyname}: ${policy.cmd} for ${policy.roles}`);
      });
    }

    console.log('\n🎉 Student login should now work without teacher login!');
    console.log('🔗 Try accessing: http://localhost:3000/student/login');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testStudentLogin();
