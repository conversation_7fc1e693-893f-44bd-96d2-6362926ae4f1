-- Setup settings table with all required fields

-- First, check if the settings table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'settings') THEN
        -- Create settings table if it doesn't exist
        CREATE TABLE settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  kkm INTEGER NOT NULL DEFAULT 70,
  pre_test_questions INTEGER NOT NULL DEFAULT 5,
  main_test_questions INTEGER NOT NULL DEFAULT 10,
  enable_ai BOOLEAN NOT NULL DEFAULT true,
  ai_model TEXT NOT NULL DEFAULT 'gemini',
  ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
  school_name TEXT DEFAULT 'Science Academy',
  school_logo_url TEXT,
  theme_color TEXT DEFAULT '#4F46E5',
  enable_student_registration BOOLEAN DEFAULT true,
  teacher_name TEXT DEFAULT 'Ms. <PERSON>',
  grade_level TEXT DEFAULT '5',
  subject TEXT DEFAULT 'science',
  topic TEXT DEFAULT 'Energy Transformation',
  academic_year TEXT DEFAULT '2023-2024',
  auto_grade BOOLEAN DEFAULT true,
  show_explanation BOOLEAN DEFAULT true,
  allow_retry BOOLEAN DEFAULT true,
  max_retries INTEGER DEFAULT 3,
  difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}',
  notify_teacher BOOLEAN DEFAULT true,
  notify_parent BOOLEAN DEFAULT false,
  theme TEXT DEFAULT 'default',
  email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);
    ELSE
        -- Table exists, add any missing columns

        -- Check and add teacher_name column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'teacher_name') THEN
            ALTER TABLE settings ADD COLUMN teacher_name TEXT DEFAULT 'Ms. Johnson';
        END IF;

        -- Check and add grade_level column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'grade_level') THEN
            ALTER TABLE settings ADD COLUMN grade_level TEXT DEFAULT '5';
        END IF;

        -- Check and add subject column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'subject') THEN
            ALTER TABLE settings ADD COLUMN subject TEXT DEFAULT 'science';
        END IF;

        -- Check and add topic column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'topic') THEN
            ALTER TABLE settings ADD COLUMN topic TEXT DEFAULT 'Energy Transformation';
        END IF;

        -- Check and add academic_year column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'academic_year') THEN
            ALTER TABLE settings ADD COLUMN academic_year TEXT DEFAULT '2023-2024';
        END IF;

        -- Check and add auto_grade column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'auto_grade') THEN
            ALTER TABLE settings ADD COLUMN auto_grade BOOLEAN DEFAULT true;
        END IF;

        -- Check and add show_explanation column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'show_explanation') THEN
            ALTER TABLE settings ADD COLUMN show_explanation BOOLEAN DEFAULT true;
        END IF;

        -- Check and add allow_retry column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'allow_retry') THEN
            ALTER TABLE settings ADD COLUMN allow_retry BOOLEAN DEFAULT true;
        END IF;

        -- Check and add max_retries column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'max_retries') THEN
            ALTER TABLE settings ADD COLUMN max_retries INTEGER DEFAULT 3;
        END IF;

        -- Check and add difficulty_distribution column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'difficulty_distribution') THEN
            ALTER TABLE settings ADD COLUMN difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}';
        END IF;

        -- Check and add notify_teacher column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'notify_teacher') THEN
            ALTER TABLE settings ADD COLUMN notify_teacher BOOLEAN DEFAULT true;
        END IF;

        -- Check and add notify_parent column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'notify_parent') THEN
            ALTER TABLE settings ADD COLUMN notify_parent BOOLEAN DEFAULT false;
        END IF;

        -- Check and add theme column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'theme') THEN
            ALTER TABLE settings ADD COLUMN theme TEXT DEFAULT 'default';
        END IF;

        -- Check and add email_template column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'email_template') THEN
            ALTER TABLE settings ADD COLUMN email_template TEXT DEFAULT 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]';
        END IF;

        -- Check and add ai_model column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'ai_model') THEN
            ALTER TABLE settings ADD COLUMN ai_model TEXT DEFAULT 'gemini';
        END IF;

        -- Check and add ai_prompt column
        IF NOT EXISTS (SELECT FROM information_schema.columns
                      WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'ai_prompt') THEN
            ALTER TABLE settings ADD COLUMN ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]';
        END IF;
    END IF;
END $$;

-- Insert default settings if the table is empty
DO $$
DECLARE
    settings_count INTEGER;
BEGIN
    -- Check if settings table is empty
    SELECT COUNT(*) INTO settings_count FROM settings;

    IF settings_count = 0 THEN
        -- Insert default settings with only the columns that exist
        INSERT INTO settings (
            kkm,
            pre_test_questions,
            main_test_questions,
            enable_ai
        )
        VALUES (
            70,
            5,
            10,
            true
        );

        -- Update with additional fields that might exist
        UPDATE settings
        SET
            ai_model = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'ai_model')
                      THEN 'gemini' ELSE ai_model END,

            ai_prompt = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'ai_prompt')
                      THEN 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]' ELSE ai_prompt END,

            school_name = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'school_name')
                      THEN 'Science Academy' ELSE school_name END,

            theme_color = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'theme_color')
                      THEN '#4F46E5' ELSE theme_color END,

            enable_student_registration = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'enable_student_registration')
                      THEN true ELSE enable_student_registration END,

            teacher_name = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'teacher_name')
                      THEN 'Ms. Johnson' ELSE teacher_name END,

            grade_level = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'grade_level')
                      THEN '5' ELSE grade_level END,

            subject = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'subject')
                      THEN 'science' ELSE subject END,

            topic = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'topic')
                      THEN 'Energy Transformation' ELSE topic END,

            academic_year = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'academic_year')
                      THEN '2023-2024' ELSE academic_year END,

            auto_grade = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'auto_grade')
                      THEN true ELSE auto_grade END,

            show_explanation = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'show_explanation')
                      THEN true ELSE show_explanation END,

            allow_retry = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'allow_retry')
                      THEN true ELSE allow_retry END,

            max_retries = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'max_retries')
                      THEN 3 ELSE max_retries END,

            difficulty_distribution = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'difficulty_distribution')
                      THEN '{"easy": 30, "medium": 40, "hard": 30}'::jsonb ELSE difficulty_distribution END,

            notify_teacher = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'notify_teacher')
                      THEN true ELSE notify_teacher END,

            notify_parent = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'notify_parent')
                      THEN false ELSE notify_parent END,

            theme = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'theme')
                      THEN 'default' ELSE theme END,

            email_template = CASE WHEN EXISTS (SELECT FROM information_schema.columns
                          WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'email_template')
                      THEN 'Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]' ELSE email_template END
        WHERE id IS NOT NULL;
    END IF;
END $$;

-- Create function to update the updated_at timestamp if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        CREATE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $BODY$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $BODY$ LANGUAGE plpgsql;
    END IF;
END $$;

-- Create trigger to update the updated_at timestamp if the column exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.columns
              WHERE table_schema = 'public' AND table_name = 'settings' AND column_name = 'updated_at') THEN

        -- Drop the trigger if it exists
        DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;

        -- Create the trigger
        CREATE TRIGGER update_settings_updated_at
        BEFORE UPDATE ON settings
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Verify settings
SELECT * FROM settings LIMIT 1;
