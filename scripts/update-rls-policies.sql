-- Update Row Level Security (RLS) policies for the questions table

-- First, let's check if <PERSON><PERSON> is enabled on the questions table
SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = 'questions';

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Questions accessible by all authenticated users" ON public.questions;
DROP POLICY IF EXISTS "Questions insertable by authenticated users" ON public.questions;
DROP POLICY IF EXISTS "Questions updatable by authenticated users" ON public.questions;
DROP POLICY IF EXISTS "Questions deletable by authenticated users" ON public.questions;

-- Create policies for the questions table

-- 1. Allow authenticated users to select (read) questions
CREATE POLICY "Questions accessible by all authenticated users"
  ON public.questions FOR SELECT
  TO authenticated
  USING (true);

-- 2. Allow authenticated users to insert questions
CREATE POLICY "Questions insertable by authenticated users"
  ON public.questions FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- 3. Allow authenticated users to update questions
CREATE POLICY "Questions updatable by authenticated users"
  ON public.questions FOR UPDATE
  TO authenticated
  USING (true);

-- 4. Allow authenticated users to delete questions
CREATE POLICY "Questions deletable by authenticated users"
  ON public.questions FOR DELETE
  TO authenticated
  USING (true);

-- If you want to restrict these operations to only teachers, you can use a condition like:
-- USING (EXISTS (SELECT 1 FROM teachers WHERE auth_id = auth.uid()))

-- Make sure RLS is enabled on the questions table
ALTER TABLE public.questions ENABLE ROW LEVEL SECURITY;

-- Verify the policies
SELECT * FROM pg_policies WHERE tablename = 'questions';
