-- Create tables for the science assessment app

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better data integrity
DO $$ BEGIN
    CREATE TYPE difficulty_level AS ENUM ('easy', 'medium', 'hard');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE question_type_enum AS ENUM ('pre-test', 'main-test');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE test_type_enum AS ENUM ('pre-test', 'main-test');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Questions table
CREATE TABLE IF NOT EXISTS questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  text TEXT NOT NULL,
  options JSONB NOT NULL,
  correct_answer TEXT NOT NULL,
  difficulty difficulty_level NOT NULL,
  explanation TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  question_type TEXT NOT NULL CHECK (question_type IN ('pre-test', 'main-test')),
  subject TEXT DEFAULT 'science',
  topic TEXT DEFAULT 'energy transformation',
  grade_level INTEGER DEFAULT 5
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  student_id TEXT NOT NULL UNIQUE,
  current_level TEXT NOT NULL DEFAULT 'easy' CHECK (current_level IN ('easy', 'medium', 'hard')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  grade INTEGER DEFAULT 5,
  class_section TEXT,
  last_login TIMESTAMP WITH TIME ZONE
);

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  auth_id UUID UNIQUE REFERENCES auth.users(id),
  name TEXT,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);

-- Classes table
CREATE TABLE IF NOT EXISTS classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  teacher_id UUID REFERENCES teachers(id),
  grade_level INTEGER NOT NULL,
  section TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);

-- Student-Class relationship (many-to-many)
CREATE TABLE IF NOT EXISTS student_classes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID REFERENCES students(id) ON DELETE CASCADE,
  class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(student_id, class_id)
);

-- Test results table
CREATE TABLE IF NOT EXISTS test_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id TEXT NOT NULL REFERENCES students(student_id),
  test_type TEXT NOT NULL CHECK (test_type IN ('pre-test', 'main-test')),
  score INTEGER NOT NULL,
  total_questions INTEGER NOT NULL,
  passed BOOLEAN NOT NULL,
  previous_level TEXT CHECK (previous_level IN ('easy', 'medium', 'hard')),
  new_level TEXT NOT NULL CHECK (new_level IN ('easy', 'medium', 'hard')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  time_spent INTEGER, -- in seconds
  subject TEXT DEFAULT 'science',
  topic TEXT DEFAULT 'energy transformation'
);

-- Student answers table (to track individual question responses)
CREATE TABLE IF NOT EXISTS student_answers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  test_result_id UUID REFERENCES test_results(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE SET NULL,
  student_answer TEXT NOT NULL,
  is_correct BOOLEAN NOT NULL,
  time_spent INTEGER, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  kkm INTEGER NOT NULL DEFAULT 70,
  pre_test_questions INTEGER NOT NULL DEFAULT 15,
  main_test_questions INTEGER NOT NULL DEFAULT 20,
  enable_ai BOOLEAN NOT NULL DEFAULT true,
  ai_model TEXT NOT NULL DEFAULT 'gpt-4o',
  ai_prompt TEXT NOT NULL DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  school_name TEXT DEFAULT 'Science Academy',
  school_logo_url TEXT,
  theme_color TEXT DEFAULT '#4F46E5',
  enable_student_registration BOOLEAN DEFAULT true
);

-- Insert default settings
INSERT INTO settings (
  kkm,
  pre_test_questions,
  main_test_questions,
  enable_ai,
  ai_model,
  ai_prompt,
  school_name,
  theme_color,
  enable_student_registration
) VALUES (
  70,
  15,
  20,
  true,
  'gpt-4o',
  'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
  'Science Academy',
  '#4F46E5',
  true
) ON CONFLICT DO NOTHING;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(question_type);
CREATE INDEX IF NOT EXISTS idx_questions_subject_topic ON questions(subject, topic);
CREATE INDEX IF NOT EXISTS idx_students_current_level ON students(current_level);
CREATE INDEX IF NOT EXISTS idx_test_results_student_id ON test_results(student_id);
CREATE INDEX IF NOT EXISTS idx_test_results_test_type ON test_results(test_type);
CREATE INDEX IF NOT EXISTS idx_test_results_created_at ON test_results(created_at);
CREATE INDEX IF NOT EXISTS idx_student_answers_test_result_id ON student_answers(test_result_id);
CREATE INDEX IF NOT EXISTS idx_student_answers_is_correct ON student_answers(is_correct);

-- Create RLS (Row Level Security) policies
-- This ensures data security at the database level

-- Enable RLS on tables
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Questions: accessible by all authenticated users
CREATE POLICY "Questions accessible by all authenticated users"
  ON questions FOR SELECT
  TO authenticated
  USING (true);

-- Students: teachers can see all, students can only see themselves
CREATE POLICY "Students can view their own data"
  ON students FOR SELECT
  TO authenticated
  USING (auth.uid()::text = student_id OR
         EXISTS (SELECT 1 FROM teachers WHERE auth_id = auth.uid()));

-- Test results: teachers can see all, students can only see their own
CREATE POLICY "Test results viewable by student or teacher"
  ON test_results FOR SELECT
  TO authenticated
  USING (student_id = (SELECT student_id FROM students WHERE student_id = auth.uid()::text) OR
         EXISTS (SELECT 1 FROM teachers WHERE auth_id = auth.uid()));

-- Settings: viewable by all authenticated users
CREATE POLICY "Settings viewable by all authenticated users"
  ON settings FOR SELECT
  TO authenticated
  USING (true);

-- Settings: only updatable by teachers
CREATE POLICY "Settings updatable by teachers only"
  ON settings FOR UPDATE
  TO authenticated
  USING (EXISTS (SELECT 1 FROM teachers WHERE auth_id = auth.uid()));

-- Create functions and triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to update timestamps
CREATE TRIGGER update_questions_modtime
BEFORE UPDATE ON questions
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_students_modtime
BEFORE UPDATE ON students
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_teachers_modtime
BEFORE UPDATE ON teachers
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_classes_modtime
BEFORE UPDATE ON classes
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_settings_modtime
BEFORE UPDATE ON settings
FOR EACH ROW EXECUTE FUNCTION update_modified_column();
