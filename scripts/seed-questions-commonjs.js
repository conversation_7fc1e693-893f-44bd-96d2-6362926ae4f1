// This script can be used to seed the Supabase database with initial questions
// Run with: node scripts/seed-questions-commonjs.js

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Since we can't directly require a TypeScript file, we'll define the questions here
// These are copied from lib/mock-data.ts
const preTestQuestions = [
  {
    id: "pre-1",
    text: "Apa yang terjadi pada energi kimia dalam baterai ketika Anda menyalakan senter?",
    options: [
      "Energi menghilang",
      "Energi berubah menjadi energi cahaya dan panas",
      "Energi tetap sama",
      "Energi berubah menjadi energi suara",
    ],
    correctAnswer: "Energi berubah menjadi energi cahaya dan panas",
    difficulty: "easy",
    explanation:
      "<PERSON><PERSON><PERSON>a menyalakan senter, energi kimia yang tersimpan dalam baterai berubah menjadi energi listrik, yang kemudian berubah menjadi energi cahaya dan sebagian energi panas.",
  },
  {
    id: "pre-2",
    text: "Transformasi energi apa yang terjadi ketika Anda menggosok-gosokkan tangan Anda?",
    options: ["Kimia menjadi panas", "Mekanik menjadi panas", "Listrik menjadi panas", "Cahaya menjadi panas"],
    correctAnswer: "Mekanik menjadi panas",
    difficulty: "easy",
    explanation:
      "Ketika Anda menggosok-gosokkan tangan, energi mekanik (gerakan) berubah menjadi energi panas karena gesekan antara kedua tangan Anda.",
  },
  {
    id: "pre-3",
    text: "Transformasi energi apa yang terjadi pada panel surya?",
    options: ["Panas menjadi listrik", "Cahaya menjadi listrik", "Kimia menjadi listrik", "Mekanik menjadi listrik"],
    correctAnswer: "Cahaya menjadi listrik",
    difficulty: "medium",
    explanation:
      "Panel surya mengandung sel fotovoltaik yang mengubah energi cahaya dari matahari menjadi energi listrik yang dapat menggerakkan perangkat atau disimpan dalam baterai.",
  },
  {
    id: "pre-4",
    text: "Pada turbin angin, apa transformasi energi utama yang terjadi?",
    options: [
      "Angin menjadi mekanik menjadi listrik",
      "Angin menjadi panas menjadi listrik",
      "Angin menjadi suara menjadi listrik",
      "Angin menjadi cahaya menjadi listrik",
    ],
    correctAnswer: "Angin menjadi mekanik menjadi listrik",
    difficulty: "medium",
    explanation:
      "Turbin angin menangkap energi kinetik angin, yang memutar bilah (energi mekanik), dan kemudian generator mengubah energi mekanik ini menjadi energi listrik.",
  },
  {
    id: "pre-5",
    text: "Transformasi energi apa yang terjadi ketika Anda berbicara ke mikrofon?",
    options: ["Suara menjadi listrik", "Mekanik menjadi suara", "Kimia menjadi suara", "Listrik menjadi suara"],
    correctAnswer: "Suara menjadi listrik",
    difficulty: "medium",
    explanation:
      "Ketika Anda berbicara ke mikrofon, gelombang suara (energi suara) menyebabkan diafragma bergetar, yang kemudian diubah menjadi energi listrik yang dapat diperkuat atau direkam.",
  }
];

const mainTestQuestions = {
  easy: [
    {
      id: "easy-1",
      text: "What energy transformation occurs in a light bulb?",
      options: ["Chemical to light", "Electrical to light and heat", "Nuclear to light", "Mechanical to light"],
      correctAnswer: "Electrical to light and heat",
      difficulty: "easy",
      explanation:
        "A light bulb transforms electrical energy into light energy and heat energy. Most traditional incandescent bulbs actually convert more energy to heat than to light.",
    },
    {
      id: "easy-2",
      text: "Which energy transformation happens when you use a battery-powered toy?",
      options: [
        "Chemical to electrical to mechanical",
        "Electrical to chemical to mechanical",
        "Mechanical to electrical to chemical",
        "Heat to electrical to mechanical",
      ],
      correctAnswer: "Chemical to electrical to mechanical",
      difficulty: "easy",
      explanation:
        "In a battery-powered toy, chemical energy stored in the battery is transformed into electrical energy, which powers motors that create mechanical energy (movement).",
    }
  ],
  medium: [
    {
      id: "medium-1",
      text: "What energy transformation occurs in a microphone?",
      options: ["Electrical to sound", "Sound to electrical", "Mechanical to sound", "Chemical to sound"],
      correctAnswer: "Sound to electrical",
      difficulty: "medium",
      explanation:
        "A microphone transforms sound energy (sound waves) into electrical energy. The sound waves cause a diaphragm to vibrate, which creates corresponding electrical signals.",
    },
    {
      id: "medium-2",
      text: "Which energy transformation happens during cellular respiration?",
      options: [
        "Light to chemical",
        "Chemical to heat and ATP (chemical)",
        "Electrical to chemical",
        "Mechanical to chemical",
      ],
      correctAnswer: "Chemical to heat and ATP (chemical)",
      difficulty: "medium",
      explanation:
        "During cellular respiration, the chemical energy stored in glucose is transformed into ATP (another form of chemical energy) and heat energy. ATP is the energy currency that cells use to power various processes.",
    }
  ],
  hard: [
    {
      id: "hard-1",
      text: "What energy transformations occur in a nuclear power plant?",
      options: [
        "Nuclear to heat to mechanical to electrical",
        "Chemical to heat to mechanical to electrical",
        "Potential to kinetic to electrical",
        "Electrical to nuclear to heat",
      ],
      correctAnswer: "Nuclear to heat to mechanical to electrical",
      difficulty: "hard",
      explanation:
        "In a nuclear power plant, nuclear energy from uranium is transformed into heat energy through nuclear fission. This heat converts water to steam, which drives turbines (mechanical energy), which then generate electrical energy.",
    },
    {
      id: "hard-2",
      text: "Which energy transformation best describes what happens in a piezoelectric crystal when it's compressed?",
      options: ["Chemical to electrical", "Mechanical to electrical", "Heat to electrical", "Light to electrical"],
      correctAnswer: "Mechanical to electrical",
      difficulty: "hard",
      explanation:
        "When a piezoelectric crystal is compressed (mechanical energy), it generates a small electrical voltage (electrical energy). This property is used in many devices like electric lighters and some microphones.",
    }
  ]
};

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function seedQuestions() {
  console.log('Starting to seed questions...');

  // Seed pre-test questions
  for (const question of preTestQuestions) {
    const { data, error } = await supabase
      .from('questions')
      .insert({
        text: question.text,
        options: question.options,
        correct_answer: question.correctAnswer,
        difficulty: question.difficulty,
        explanation: question.explanation,
        question_type: 'pre-test',
        subject: 'science',
        topic: 'energy transformation',
        grade_level: 5
      });

    if (error) {
      console.error(`Error inserting pre-test question: ${question.text}`, error);
    } else {
      console.log(`Inserted pre-test question: ${question.text.substring(0, 30)}...`);
    }
  }

  console.log(`Inserted ${preTestQuestions.length} pre-test questions`);

  // Seed main test questions
  let mainTestCount = 0;

  for (const difficulty of ['easy', 'medium', 'hard']) {
    const questions = mainTestQuestions[difficulty];

    for (const question of questions) {
      const { data, error } = await supabase
        .from('questions')
        .insert({
          text: question.text,
          options: question.options,
          correct_answer: question.correctAnswer,
          difficulty: question.difficulty,
          explanation: question.explanation,
          question_type: 'main-test',
          subject: 'science',
          topic: 'energy transformation',
          grade_level: 5
        });

      if (error) {
        console.error(`Error inserting main test question: ${question.text}`, error);
      } else {
        console.log(`Inserted main test question (${difficulty}): ${question.text.substring(0, 30)}...`);
      }

      mainTestCount++;
    }
  }

  console.log(`Inserted ${mainTestCount} main test questions`);
  console.log('Seeding completed!');
}

// Run the seeding function
seedQuestions()
  .catch(error => {
    console.error('Error during seeding:', error);
  })
  .finally(() => {
    console.log('Seed script execution completed.');
  });
