#!/bin/bash

# Setup settings table and ensure all fields are present
echo "Setting up settings table..."

# Try the simple setup script first
echo "Trying simple setup..."
node scripts/setup-settings-simple.js

# If that fails, try the direct database connection
if [ $? -ne 0 ]; then
  echo "Simple setup failed. Trying direct database connection..."

  # Check if pg package is installed
  if ! npm list pg | grep -q pg; then
    echo "Installing required dependencies..."
    npm install pg
  fi

  # Run the direct database connection script
  node scripts/create-settings-table.js
fi

echo "Settings table setup complete."
echo ""
echo "If you're still having issues, you can manually run the SQL script in the Supabase SQL Editor:"
echo "1. Open the Supabase dashboard"
echo "2. Go to the SQL Editor"
echo "3. Copy and paste the contents of scripts/setup-settings-simple.sql"
echo "4. Run the script"
