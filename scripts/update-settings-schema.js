// Update settings table schema to include all fields
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function updateSettingsSchema() {
  try {
    console.log('Checking settings table...');

    // Check if settings table exists
    const { data: tableExists, error: tableError } = await supabase.sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = 'settings'
    `;

    if (tableError) {
      console.error('Error checking if settings table exists:', tableError);
      return;
    }

    if (!tableExists || tableExists.data.length === 0) {
      console.log('Settings table does not exist. Creating it...');

      // Create settings table with all fields
      const { error: createError } = await supabase.sql`
        CREATE TABLE settings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          kkm INTEGER NOT NULL DEFAULT 70,
          pre_test_questions INTEGER NOT NULL DEFAULT 5,
          main_test_questions INTEGER NOT NULL DEFAULT 10,
          enable_ai BOOLEAN NOT NULL DEFAULT true,
          ai_model TEXT NOT NULL DEFAULT 'gemini',
          ai_prompt TEXT DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name TEXT DEFAULT 'Science Academy',
          school_logo_url TEXT,
          theme_color TEXT DEFAULT '#4F46E5',
          enable_student_registration BOOLEAN DEFAULT true,
          teacher_name TEXT DEFAULT 'Ms. Johnson',
          grade_level TEXT DEFAULT '5',
          subject TEXT DEFAULT 'science',
          topic TEXT DEFAULT 'Energy Transformation',
          academic_year TEXT DEFAULT '2023-2024',
          auto_grade BOOLEAN DEFAULT true,
          show_explanation BOOLEAN DEFAULT true,
          allow_retry BOOLEAN DEFAULT true,
          max_retries INTEGER DEFAULT 3,
          difficulty_distribution JSONB DEFAULT '{"easy": 30, "medium": 40, "hard": 30}',
          notify_teacher BOOLEAN DEFAULT true,
          notify_parent BOOLEAN DEFAULT false,
          theme TEXT DEFAULT 'default',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE
        );
      `;

      if (createError) {
        console.error('Error creating settings table:', createError);
        return;
      }

      console.log('Settings table created successfully.');
    } else {
      console.log('Settings table exists. Adding missing columns...');

      // List of columns to check and add if missing
      const columns = [
        { name: 'teacher_name', type: 'TEXT', default: "'Ms. Johnson'" },
        { name: 'grade_level', type: 'TEXT', default: "'5'" },
        { name: 'subject', type: 'TEXT', default: "'science'" },
        { name: 'topic', type: 'TEXT', default: "'Energy Transformation'" },
        { name: 'academic_year', type: 'TEXT', default: "'2023-2024'" },
        { name: 'auto_grade', type: 'BOOLEAN', default: 'true' },
        { name: 'show_explanation', type: 'BOOLEAN', default: 'true' },
        { name: 'allow_retry', type: 'BOOLEAN', default: 'true' },
        { name: 'max_retries', type: 'INTEGER', default: '3' },
        { name: 'difficulty_distribution', type: 'JSONB', default: "'{\"easy\": 30, \"medium\": 40, \"hard\": 30}'" },
        { name: 'notify_teacher', type: 'BOOLEAN', default: 'true' },
        { name: 'notify_parent', type: 'BOOLEAN', default: 'false' },
        { name: 'theme', type: 'TEXT', default: "'default'" },
        { name: 'email_template', type: 'TEXT', default: "'Dear [RECIPIENT_NAME],\\n\\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\\n\\n[PASS_FAIL_MESSAGE]\\n\\nYou can view the detailed results by logging into the assessment platform.\\n\\nBest regards,\\n[TEACHER_NAME]\\n[SCHOOL_NAME]'" }
      ];

      // Check each column and add if missing
      for (const column of columns) {
        const { data: columnExists, error: columnError } = await supabase.sql`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = 'settings'
          AND column_name = ${column.name}
        `;

        if (columnError) {
          console.error(`Error checking if ${column.name} column exists:`, columnError);
          continue;
        }

        if (!columnExists || columnExists.data.length === 0) {
          console.log(`${column.name} column does not exist. Adding it...`);

          // Add column
          const { error: addColumnError } = await supabase.sql`
            ALTER TABLE settings ADD COLUMN IF NOT EXISTS ${supabase.sql(column.name)} ${supabase.sql(column.type)} DEFAULT ${supabase.sql(column.default)};
          `;

          if (addColumnError) {
            console.error(`Error adding ${column.name} column:`, addColumnError);
          } else {
            console.log(`${column.name} column added successfully.`);
          }
        } else {
          console.log(`${column.name} column exists.`);
        }
      }
    }

    // Check if settings table has any records
    const { data: settingsData, error: settingsError } = await supabase.sql`
      SELECT COUNT(*) FROM settings
    `;

    if (settingsError) {
      console.error('Error checking settings count:', settingsError);
      return;
    }

    if (!settingsData || parseInt(settingsData.data[0].count) === 0) {
      console.log('Settings table is empty. Inserting default settings...');

      // Insert default settings with all fields
      const { error: insertError } = await supabase
        .from('settings')
        .insert({
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_model: 'gemini',
          ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name: 'Science Academy',
          school_logo_url: null,
          theme_color: '#4F46E5',
          enable_student_registration: true,
          teacher_name: 'Ms. Johnson',
          grade_level: '5',
          subject: 'science',
          topic: 'Energy Transformation',
          academic_year: '2023-2024',
          auto_grade: true,
          show_explanation: true,
          allow_retry: true,
          max_retries: 3,
          difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
          notify_teacher: true,
          notify_parent: false,
          theme: 'default',
          email_template: 'Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]'
        });

      if (insertError) {
        console.error('Error inserting default settings:', insertError);
        return;
      }

      console.log('Default settings inserted successfully.');
    } else {
      console.log('Settings table has records. Updating with default values for new columns...');

      // Get existing settings
      const { data: existingSettingsResult, error: fetchError } = await supabase.sql`
        SELECT * FROM settings LIMIT 1
      `;

      const existingSettings = existingSettingsResult?.data?.[0] || {};

      if (fetchError) {
        console.error('Error fetching existing settings:', fetchError);
        return;
      }

      // Update with default values for new columns if they're null
      const updateData = {
        teacher_name: existingSettings.teacher_name || 'Ms. Johnson',
        grade_level: existingSettings.grade_level || '5',
        subject: existingSettings.subject || 'science',
        topic: existingSettings.topic || 'Energy Transformation',
        academic_year: existingSettings.academic_year || '2023-2024',
        auto_grade: existingSettings.auto_grade !== null ? existingSettings.auto_grade : true,
        show_explanation: existingSettings.show_explanation !== null ? existingSettings.show_explanation : true,
        allow_retry: existingSettings.allow_retry !== null ? existingSettings.allow_retry : true,
        max_retries: existingSettings.max_retries || 3,
        difficulty_distribution: existingSettings.difficulty_distribution || { easy: 30, medium: 40, hard: 30 },
        notify_teacher: existingSettings.notify_teacher !== null ? existingSettings.notify_teacher : true,
        notify_parent: existingSettings.notify_parent !== null ? existingSettings.notify_parent : false,
        theme: existingSettings.theme || 'default',
        email_template: existingSettings.email_template || 'Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]',
        updated_at: new Date().toISOString()
      };

      // Update settings
      const { error: updateError } = await supabase.sql`
        UPDATE settings
        SET
          teacher_name = ${updateData.teacher_name},
          grade_level = ${updateData.grade_level},
          subject = ${updateData.subject},
          topic = ${updateData.topic},
          academic_year = ${updateData.academic_year},
          auto_grade = ${updateData.auto_grade},
          show_explanation = ${updateData.show_explanation},
          allow_retry = ${updateData.allow_retry},
          max_retries = ${updateData.max_retries},
          difficulty_distribution = ${JSON.stringify(updateData.difficulty_distribution)}::jsonb,
          notify_teacher = ${updateData.notify_teacher},
          notify_parent = ${updateData.notify_parent},
          theme = ${updateData.theme},
          email_template = ${updateData.email_template},
          updated_at = ${updateData.updated_at}
        WHERE id = ${existingSettings.id}
      `;

      if (updateError) {
        console.error('Error updating settings with default values:', updateError);
        return;
      }

      console.log('Settings updated with default values for new columns.');
    }

    // Verify settings
    const { data: settingsResult, error: verifyError } = await supabase.sql`
      SELECT * FROM settings LIMIT 1
    `;

    const settings = settingsResult?.data || [];

    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }

    console.log('Current settings:', settings[0] || 'No settings found');
    console.log('Settings table update complete.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateSettingsSchema()
  .then(() => {
    console.log('Settings schema update complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in update process:', error);
    process.exit(1);
  });
