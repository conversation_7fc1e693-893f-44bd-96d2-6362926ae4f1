// Run SQL script
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runSqlScript(scriptPath) {
  try {
    // Read the SQL file
    const sqlFilePath = path.resolve(__dirname, scriptPath);
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL content into individual statements
    const statements = sqlContent
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .filter(statement => statement.trim() !== '');
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        
        try {
          const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
          
          if (error) {
            console.error(`Error executing statement ${i + 1}:`, error);
            
            // Try direct SQL execution as fallback
            console.log('Trying direct SQL execution...');
            const { error: sqlError } = await supabase.sql(statement);
            
            if (sqlError) {
              console.error('Direct SQL execution failed:', sqlError);
            } else {
              console.log(`Statement ${i + 1} executed successfully via direct SQL`);
            }
          } else {
            console.log(`Statement ${i + 1} executed successfully`);
          }
        } catch (execError) {
          console.error(`Exception executing statement ${i + 1}:`, execError);
        }
      }
    }
    
    console.log('SQL script execution completed');
    
  } catch (error) {
    console.error('Error running SQL script:', error);
  }
}

// Check command line arguments
const scriptPath = process.argv[2];
if (!scriptPath) {
  console.error('Please provide the path to the SQL script as an argument');
  console.error('Example: node run-sql.js update-settings-table.sql');
  process.exit(1);
}

runSqlScript(scriptPath)
  .then(() => {
    console.log('Script execution complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in script execution:', error);
    process.exit(1);
  });
