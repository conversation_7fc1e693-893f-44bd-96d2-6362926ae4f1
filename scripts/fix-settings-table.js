// Fix settings table to ensure it has all required columns
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixSettingsTable() {
  try {
    console.log('Checking settings table...');

    // Check if settings table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'settings');

    if (tableError) {
      console.error('Error checking if settings table exists:', tableError);
      return;
    }

    if (!tableExists || tableExists.length === 0) {
      console.log('Settings table does not exist. Creating it...');

      // Create settings table
      const { error: createError } = await supabase.sql`
        CREATE TABLE settings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          kkm INTEGER NOT NULL DEFAULT 70,
          pre_test_questions INTEGER NOT NULL DEFAULT 5,
          main_test_questions INTEGER NOT NULL DEFAULT 10,
          enable_ai BOOLEAN NOT NULL DEFAULT true,
          ai_model TEXT NOT NULL DEFAULT 'gemini',
          ai_prompt TEXT NOT NULL DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE,
          school_name TEXT DEFAULT 'Science Academy',
          school_logo_url TEXT,
          theme_color TEXT DEFAULT '#4F46E5',
          enable_student_registration BOOLEAN DEFAULT true
        );
      `;

      if (createError) {
        console.error('Error creating settings table:', createError);
        return;
      }

      console.log('Settings table created successfully.');
    } else {
      console.log('Settings table exists. Checking columns...');

      // Check if ai_model column exists
      const { data: columnExists, error: columnError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'settings')
        .eq('column_name', 'ai_model');

      if (columnError) {
        console.error('Error checking if ai_model column exists:', columnError);
        return;
      }

      if (!columnExists || columnExists.length === 0) {
        console.log('ai_model column does not exist. Adding it...');

        // Add ai_model column
        const { error: addColumnError } = await supabase.sql`
          ALTER TABLE settings ADD COLUMN IF NOT EXISTS ai_model TEXT NOT NULL DEFAULT 'gemini';
        `;

        if (addColumnError) {
          console.error('Error adding ai_model column:', addColumnError);
          return;
        }

        console.log('ai_model column added successfully.');
      } else {
        console.log('ai_model column exists.');
      }

      // Check if ai_prompt column exists
      const { data: promptColumnExists, error: promptColumnError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'settings')
        .eq('column_name', 'ai_prompt');

      if (promptColumnError) {
        console.error('Error checking if ai_prompt column exists:', promptColumnError);
        return;
      }

      if (!promptColumnExists || promptColumnExists.length === 0) {
        console.log('ai_prompt column does not exist. Adding it...');

        // Add ai_prompt column
        const { error: addPromptColumnError } = await supabase.sql`
          ALTER TABLE settings ADD COLUMN IF NOT EXISTS ai_prompt TEXT;
        `;

        // Set default value for ai_prompt column
        if (!addPromptColumnError) {
          const { error: setDefaultError } = await supabase.sql`
            UPDATE settings
            SET ai_prompt = 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]'
            WHERE ai_prompt IS NULL OR ai_prompt = '';
          `;

          if (setDefaultError) {
            console.error('Error setting default value for ai_prompt column:', setDefaultError);
          } else {
            console.log('Default value set for ai_prompt column.');
          }
        }

        if (addPromptColumnError) {
          console.error('Error adding ai_prompt column:', addPromptColumnError);
          return;
        }

        console.log('ai_prompt column added successfully.');
      } else {
        console.log('ai_prompt column exists.');
      }
    }

    // Check if settings table has any records
    const { data: settingsData, error: settingsError } = await supabase
      .from('settings')
      .select('count(*)')
      .single();

    if (settingsError) {
      console.error('Error checking settings count:', settingsError);
      return;
    }

    if (!settingsData || settingsData.count === 0) {
      console.log('Settings table is empty. Inserting default settings...');

      // Insert default settings
      const { error: insertError } = await supabase
        .from('settings')
        .insert({
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_model: 'gemini',
          ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name: 'Science Academy',
          theme_color: '#4F46E5',
          enable_student_registration: true
        });

      if (insertError) {
        console.error('Error inserting default settings:', insertError);
        return;
      }

      console.log('Default settings inserted successfully.');
    } else {
      console.log('Settings table has records.');
    }

    // Verify settings
    const { data: settings, error: verifyError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);

    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }

    console.log('Current settings:', settings[0]);
    console.log('Settings table check complete.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

fixSettingsTable()
  .then(() => {
    console.log('Settings table fix complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in fix process:', error);
    process.exit(1);
  });
