// Enable AI in settings
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function enableAI() {
  try {
    // First, check if settings table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'settings');
    
    if (tableError) {
      console.error('Error checking if settings table exists:', tableError);
      
      // Try a different approach
      try {
        const { data: settingsData, error: settingsError } = await supabase
          .from('settings')
          .select('count(*)')
          .limit(1);
        
        if (settingsError) {
          console.error('Settings table does not exist. Creating it...');
          
          // Create settings table
          const { error: createError } = await supabase.sql`
            CREATE TABLE IF NOT EXISTS settings (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              kkm INTEGER NOT NULL DEFAULT 70,
              pre_test_questions INTEGER NOT NULL DEFAULT 5,
              main_test_questions INTEGER NOT NULL DEFAULT 20,
              enable_ai BOOLEAN NOT NULL DEFAULT true,
              ai_model TEXT NOT NULL DEFAULT 'gemini',
              ai_prompt TEXT NOT NULL DEFAULT 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE,
              school_name TEXT DEFAULT 'Science Academy',
              school_logo_url TEXT,
              theme_color TEXT DEFAULT '#4F46E5',
              enable_student_registration BOOLEAN DEFAULT true
            );
          `;
          
          if (createError) {
            console.error('Error creating settings table:', createError);
            return;
          }
          
          console.log('Settings table created successfully');
        }
      } catch (error) {
        console.error('Error checking settings table:', error);
        return;
      }
    }
    
    // Check if settings table has any records
    const { data: existingSettings, error: countError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (countError) {
      console.error('Error checking settings count:', countError);
      return;
    }
    
    if (!existingSettings || existingSettings.length === 0) {
      // Insert default settings
      const { error: insertError } = await supabase
        .from('settings')
        .insert({
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_model: 'gemini',
          ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name: 'Science Academy',
          theme_color: '#4F46E5',
          enable_student_registration: true
        });
      
      if (insertError) {
        console.error('Error inserting default settings:', insertError);
        return;
      }
      
      console.log('Default settings inserted successfully');
    } else {
      // Update existing settings
      const { error: updateError } = await supabase
        .from('settings')
        .update({
          enable_ai: true,
          ai_model: 'gemini',
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSettings[0].id);
      
      if (updateError) {
        console.error('Error updating settings:', updateError);
        return;
      }
      
      console.log('Settings updated successfully');
    }
    
    // Verify settings
    const { data: updatedSettings, error: verifyError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }
    
    console.log('Current settings:', updatedSettings[0]);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

enableAI()
  .then(() => {
    console.log('AI settings update complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in update process:', error);
    process.exit(1);
  });
