// This script can be used to seed the Supabase database with initial questions
// Run with: node scripts/seed-questions.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { preTestQuestions, mainTestQuestions } from '../lib/mock-data.js';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function seedQuestions() {
  console.log('Starting to seed questions...');
  
  // Seed pre-test questions
  for (const question of preTestQuestions) {
    const { data, error } = await supabase
      .from('questions')
      .insert({
        text: question.text,
        options: question.options,
        correct_answer: question.correctAnswer,
        difficulty: question.difficulty,
        explanation: question.explanation,
        question_type: 'pre-test'
      });
      
    if (error) {
      console.error(`Error inserting pre-test question: ${question.text}`, error);
    }
  }
  
  console.log(`Inserted ${preTestQuestions.length} pre-test questions`);
  
  // Seed main test questions
  let mainTestCount = 0;
  
  for (const difficulty of ['easy', 'medium', 'hard']) {
    const questions = mainTestQuestions[difficulty];
    
    for (const question of questions) {
      const { data, error } = await supabase
        .from('questions')
        .insert({
          text: question.text,
          options: question.options,
          correct_answer: question.correctAnswer,
          difficulty: question.difficulty,
          explanation: question.explanation,
          question_type: 'main-test'
        });
        
      if (error) {
        console.error(`Error inserting main test question: ${question.text}`, error);
      }
      
      mainTestCount++;
    }
  }
  
  console.log(`Inserted ${mainTestCount} main test questions`);
  console.log('Seeding completed!');
}

seedQuestions().catch(console.error);
