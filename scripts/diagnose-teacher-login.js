const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Diagnosing Teacher Login Issues...\n');

// Create clients
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null;

async function diagnoseTeacherLogin() {
  try {
    console.log('1. 🔧 Environment Variables Check:');
    console.log('   SUPABASE_URL:', supabaseUrl ? '✅ Present' : '❌ Missing');
    console.log('   SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Present' : '❌ Missing');
    console.log('   SUPABASE_SERVICE_KEY:', supabaseServiceKey ? '✅ Present' : '❌ Missing');
    
    if (!supabaseAdmin) {
      console.log('   ⚠️  No service key - using anon key for admin operations');
    }

    console.log('\n2. 📊 Checking Teachers Table:');
    const { data: teachers, error: teachersError } = await (supabaseAdmin || supabase)
      .from('teachers')
      .select('*');

    if (teachersError) {
      console.log('   ❌ Cannot access teachers table:', teachersError.message);
    } else {
      console.log(`   ✅ Teachers table accessible - Found ${teachers.length} teachers`);
      if (teachers.length > 0) {
        console.log('   📋 Existing teachers:');
        teachers.forEach((teacher, index) => {
          console.log(`      ${index + 1}. ${teacher.email} (ID: ${teacher.auth_id})`);
        });
      } else {
        console.log('   ⚠️  No teachers found in database');
      }
    }

    console.log('\n3. 👥 Checking Auth Users:');
    if (supabaseAdmin) {
      try {
        const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (authError) {
          console.log('   ❌ Cannot access auth users:', authError.message);
        } else {
          console.log(`   ✅ Auth users accessible - Found ${authUsers.users.length} users`);
          if (authUsers.users.length > 0) {
            console.log('   📋 Existing auth users:');
            authUsers.users.forEach((user, index) => {
              console.log(`      ${index + 1}. ${user.email} (ID: ${user.id})`);
              console.log(`         Created: ${user.created_at}`);
              console.log(`         Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
            });
          }
        }
      } catch (error) {
        console.log('   ❌ Error accessing auth users:', error.message);
      }
    } else {
      console.log('   ⚠️  Cannot check auth users without service key');
    }

    console.log('\n4. 🔐 Testing Authentication:');
    console.log('   Please provide teacher credentials to test login:');
    
    // For testing, we'll use a common test email
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';
    
    console.log(`   Testing with: ${testEmail}`);
    
    try {
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      });

      if (authError) {
        console.log('   ❌ Authentication failed:', authError.message);
        
        // Common error messages and solutions
        if (authError.message.includes('Invalid login credentials')) {
          console.log('   💡 Solution: Check if email/password is correct');
        } else if (authError.message.includes('Email not confirmed')) {
          console.log('   💡 Solution: Confirm email or disable email confirmation');
        } else if (authError.message.includes('User not found')) {
          console.log('   💡 Solution: Create teacher account first');
        }
      } else {
        console.log('   ✅ Authentication successful!');
        console.log('   👤 User ID:', authData.user.id);
        
        // Sign out after test
        await supabase.auth.signOut();
      }
    } catch (error) {
      console.log('   ❌ Authentication test failed:', error.message);
    }

    console.log('\n5. 🛠️  Recommendations:');
    
    if (!teachers || teachers.length === 0) {
      console.log('   📝 No teachers found. Create a teacher account:');
      console.log('      node scripts/setup-auth.js');
    }
    
    console.log('   📝 Manual teacher creation steps:');
    console.log('      1. Go to Supabase Dashboard > Authentication > Users');
    console.log('      2. Click "Add User"');
    console.log('      3. Enter email and password');
    console.log('      4. Go to Table Editor > teachers');
    console.log('      5. Insert row with auth_id, name, and email');
    
    console.log('\n   📝 Common issues and solutions:');
    console.log('      • Email confirmation required: Disable in Auth settings');
    console.log('      • Wrong password: Double-check password');
    console.log('      • User not in teachers table: Add record manually');
    console.log('      • RLS blocking access: Check policies');

  } catch (error) {
    console.error('❌ Diagnosis failed:', error.message);
  }
}

diagnoseTeacherLogin();
