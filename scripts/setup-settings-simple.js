// Simple setup script for settings table using only basic Supabase client methods
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Variable to track success
let setupSuccess = false;

async function createSettingsTable() {
  try {
    // We can't create tables directly with the Supabase client
    // Let's just try to insert default settings and see if it works
    console.log('Attempting to insert default settings...');
    await insertDefaultSettings();
    return true;
  } catch (error) {
    console.error('Error creating settings table:', error);
    return false;
  }
}

async function insertDefaultSettings() {
  try {
    // Insert default settings
    const { data, error: insertError } = await supabase
      .from('settings')
      .insert({
        kkm: 70,
        pre_test_questions: 5,
        main_test_questions: 10,
        enable_ai: true,
        ai_model: 'gemini',
        ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
        school_name: 'Science Academy',
        theme_color: '#4F46E5',
        enable_student_registration: true,
        teacher_name: 'Ms. Johnson',
        grade_level: '5',
        subject: 'science',
        topic: 'Energy Transformation',
        academic_year: '2023-2024',
        auto_grade: true,
        show_explanation: true,
        allow_retry: true,
        max_retries: 3,
        difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
        notify_teacher: true,
        notify_parent: false,
        theme: 'default',
        email_template: `Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
[TEACHER_NAME]
[SCHOOL_NAME]`
      })
      .select();

    if (insertError) {
      if (insertError.code === '42P01') { // relation "settings" does not exist
        console.error('Settings table does not exist. Please create it manually using the SQL script.');
        console.log('You can find the SQL script at scripts/setup-settings.sql');
      } else {
        console.error('Error inserting default settings:', insertError);
      }
      return false;
    }

    console.log('Default settings inserted successfully:', data[0]);
    return true;
  } catch (error) {
    console.error('Error inserting default settings:', error);
    return false;
  }
}

// Variable to track success
let setupSuccess = false;

// Run the setup
setupSettingsTable()
  .then(() => {
    if (setupSuccess) {
      console.log('Setup process completed successfully.');
      process.exit(0);
    } else {
      console.log('Setup process completed with errors.');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('Error in setup process:', error);
    process.exit(1);
  });

// Update the setupSettingsTable function to set the success flag
async function setupSettingsTable() {
  try {
    console.log('Checking for existing settings...');

    // Try to get settings
    const { data: existingSettings, error: fetchError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);

    if (fetchError) {
      // Table might not exist, let's try to create it
      if (fetchError.code === '42P01') { // relation "settings" does not exist
        console.log('Settings table does not exist. Creating it...');
        const created = await createSettingsTable();
        setupSuccess = created;
      } else {
        console.error('Error fetching settings:', fetchError);
        setupSuccess = false;
        return;
      }
    } else if (!existingSettings || existingSettings.length === 0) {
      // Table exists but is empty
      console.log('Settings table exists but is empty. Inserting default settings...');
      const inserted = await insertDefaultSettings();
      setupSuccess = inserted;
    } else {
      console.log('Settings table exists and has data.');
      console.log('Current settings:', existingSettings[0]);
      setupSuccess = true;
    }

    console.log('Settings table setup complete.');
  } catch (error) {
    console.error('Unexpected error:', error);
    setupSuccess = false;
  }
}
