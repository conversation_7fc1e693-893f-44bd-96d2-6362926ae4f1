// Update settings table to include AI settings
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function updateSettings() {
  try {
    // First, check if settings table exists and has any records
    const { data: existingSettings, error: fetchError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (fetchError) {
      console.error('Error fetching settings:', fetchError);
      return;
    }
    
    if (!existingSettings || existingSettings.length === 0) {
      // Insert default settings if no records exist
      const { data, error: insertError } = await supabase
        .from('settings')
        .insert({
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_model: 'gemini',
          ai_prompt: 'Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]',
          school_name: 'Science Academy',
          theme_color: '#4F46E5',
          enable_student_registration: true
        });
      
      if (insertError) {
        console.error('Error inserting default settings:', insertError);
        return;
      }
      
      console.log('Default settings inserted successfully!');
    } else {
      // Update existing settings to include AI settings
      const { data, error: updateError } = await supabase
        .from('settings')
        .update({
          enable_ai: true,
          ai_model: 'gemini',
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSettings[0].id);
      
      if (updateError) {
        console.error('Error updating settings:', updateError);
        return;
      }
      
      console.log('Settings updated successfully!');
    }
    
    // Verify settings
    const { data: updatedSettings, error: verifyError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying settings:', verifyError);
      return;
    }
    
    console.log('Current settings:', updatedSettings[0]);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateSettings()
  .then(() => {
    console.log('Settings update complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in update process:', error);
    process.exit(1);
  });
