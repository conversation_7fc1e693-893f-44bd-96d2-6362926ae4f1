// This script helps set up the database schema for the Science Assessment App
// Run with: node scripts/setup-database.js

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function setupDatabase() {
  console.log('Setting up database schema for Science Assessment App...');
  
  try {
    // Read the SQL schema file
    const schemaPath = path.join(__dirname, 'supabase-schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('SQL schema file loaded successfully.');
    console.log('This script will execute the SQL schema on your Supabase database.');
    console.log('WARNING: This may modify existing tables. Make sure you have a backup if needed.');
    
    // Ask for confirmation
    const answer = await new Promise(resolve => {
      rl.question('Do you want to proceed? (yes/no): ', resolve);
    });
    
    if (answer.toLowerCase() !== 'yes') {
      console.log('Operation cancelled.');
      return;
    }
    
    console.log('Executing SQL schema...');
    
    // Execute the SQL schema
    // Note: Supabase JS client doesn't support direct SQL execution
    // This is a placeholder - you'll need to manually execute the SQL in the Supabase dashboard
    console.log('');
    console.log('==========================================================');
    console.log('IMPORTANT: The Supabase JavaScript client does not support');
    console.log('direct SQL execution for schema creation.');
    console.log('');
    console.log('Please follow these steps:');
    console.log('1. Go to your Supabase dashboard: https://app.supabase.com/');
    console.log('2. Select your project');
    console.log('3. Go to the SQL Editor');
    console.log('4. Create a new query');
    console.log('5. Copy and paste the contents of scripts/supabase-schema.sql');
    console.log('6. Click "Run" to execute the script');
    console.log('==========================================================');
    console.log('');
    
    // Ask if they want to open the schema file
    const openFile = await new Promise(resolve => {
      rl.question('Would you like to open the schema file now? (yes/no): ', resolve);
    });
    
    if (openFile.toLowerCase() === 'yes') {
      console.log('Opening schema file...');
      console.log('');
      console.log(schemaSql);
    }
    
    console.log('');
    console.log('After setting up the schema, you can create a teacher account using:');
    console.log('node scripts/setup-auth.js');
    
  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    rl.close();
  }
}

setupDatabase();
