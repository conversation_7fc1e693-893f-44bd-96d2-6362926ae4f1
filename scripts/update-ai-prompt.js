// Update AI prompt in the settings table
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Get the new AI prompt from command line arguments
const newAiPrompt = process.argv[2] || "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]";

async function updateAiPrompt() {
  try {
    console.log('Updating AI prompt to:', newAiPrompt);
    
    // First, check if settings table exists and has records
    const { data: settings, error: fetchError } = await supabase
      .from('settings')
      .select('id')
      .limit(1);
    
    if (fetchError) {
      console.error('Error fetching settings:', fetchError);
      return;
    }
    
    if (!settings || settings.length === 0) {
      console.log('No settings found. Creating default settings with the new AI prompt...');
      
      // Insert default settings with the new AI prompt
      const { error: insertError } = await supabase
        .from('settings')
        .insert({
          kkm: 70,
          pre_test_questions: 5,
          main_test_questions: 10,
          enable_ai: true,
          ai_model: 'gemini',
          ai_prompt: newAiPrompt,
          school_name: 'Science Academy',
          theme_color: '#4F46E5',
          enable_student_registration: true
        });
      
      if (insertError) {
        console.error('Error inserting settings:', insertError);
        return;
      }
      
      console.log('Default settings created with the new AI prompt.');
    } else {
      console.log('Settings found. Updating AI prompt...');
      
      // Update AI prompt in settings
      const { error: updateError } = await supabase
        .from('settings')
        .update({
          ai_prompt: newAiPrompt,
          updated_at: new Date().toISOString()
        })
        .eq('id', settings[0].id);
      
      if (updateError) {
        console.error('Error updating AI prompt:', updateError);
        return;
      }
      
      console.log('AI prompt updated successfully.');
    }
    
    // Verify the update
    const { data: updatedSettings, error: verifyError } = await supabase
      .from('settings')
      .select('*')
      .limit(1);
    
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('Current settings:', updatedSettings[0]);
    console.log('AI prompt is now:', updatedSettings[0].ai_prompt);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateAiPrompt()
  .then(() => {
    console.log('AI prompt update complete.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error in update process:', error);
    process.exit(1);
  });
