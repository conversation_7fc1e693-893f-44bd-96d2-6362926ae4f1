// This script guides you through the Supabase setup process
// Run with: node scripts/setup-supabase.js

const readline = require('readline');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function to execute a command
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing command: ${error.message}`);
        return reject(error);
      }
      if (stderr) {
        console.error(`Command stderr: ${stderr}`);
      }
      resolve(stdout);
    });
  });
}

// Main setup function
async function setupSupabase() {
  console.log('\n=== Science Assessment App - Supabase Setup ===\n');
  console.log('This script will guide you through the process of setting up Supabase for your application.\n');
  
  // Check if .env.local exists and has Supabase credentials
  const envPath = path.join(process.cwd(), '.env.local');
  let envExists = false;
  let hasCredentials = false;
  
  try {
    if (fs.existsSync(envPath)) {
      envExists = true;
      const envContent = fs.readFileSync(envPath, 'utf8');
      hasCredentials = envContent.includes('NEXT_PUBLIC_SUPABASE_URL') && 
                       envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    }
  } catch (error) {
    console.error('Error checking .env.local file:', error);
  }
  
  // Step 1: Environment Variables
  console.log('Step 1: Environment Variables\n');
  
  if (hasCredentials) {
    console.log('✅ Supabase credentials found in .env.local file.');
  } else {
    console.log('You need to set up your Supabase credentials in the .env.local file.');
    console.log('1. Go to your Supabase project dashboard: https://app.supabase.com/');
    console.log('2. Navigate to Project Settings > API');
    console.log('3. Copy your Project URL and anon key');
    console.log('4. Add them to your .env.local file as:');
    console.log('   NEXT_PUBLIC_SUPABASE_URL=your-project-url');
    console.log('   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key');
    console.log('   SUPABASE_SERVICE_KEY=your-service-key');
    
    const proceed = await new Promise(resolve => {
      rl.question('\nHave you added your Supabase credentials to .env.local? (yes/no): ', answer => {
        resolve(answer.toLowerCase() === 'yes');
      });
    });
    
    if (!proceed) {
      console.log('\nPlease set up your Supabase credentials before continuing.');
      rl.close();
      return;
    }
  }
  
  // Step 2: Database Schema
  console.log('\nStep 2: Database Schema\n');
  console.log('You need to set up the database schema in your Supabase project.');
  console.log('Would you like to:');
  console.log('1. View instructions for setting up the schema manually');
  console.log('2. Run the database setup helper script');
  
  const schemaOption = await new Promise(resolve => {
    rl.question('\nEnter your choice (1 or 2): ', answer => {
      resolve(answer.trim());
    });
  });
  
  if (schemaOption === '1') {
    console.log('\nManual Schema Setup Instructions:');
    console.log('1. Go to your Supabase dashboard: https://app.supabase.com/');
    console.log('2. Select your project');
    console.log('3. Go to the SQL Editor');
    console.log('4. Create a new query');
    console.log('5. Open the file scripts/supabase-schema.sql in your project');
    console.log('6. Copy the entire contents of this file');
    console.log('7. Paste it into the SQL Editor in Supabase');
    console.log('8. Click "Run" to execute the script');
  } else if (schemaOption === '2') {
    console.log('\nRunning database setup helper script...\n');
    try {
      await executeCommand('node scripts/setup-database.js');
    } catch (error) {
      console.error('Error running setup-database.js:', error);
    }
  }
  
  // Step 3: Authentication
  console.log('\nStep 3: Authentication Setup\n');
  console.log('You need to set up authentication in your Supabase project.');
  console.log('Would you like to:');
  console.log('1. View instructions for setting up authentication manually');
  console.log('2. Run the teacher account creation script');
  
  const authOption = await new Promise(resolve => {
    rl.question('\nEnter your choice (1 or 2): ', answer => {
      resolve(answer.trim());
    });
  });
  
  if (authOption === '1') {
    console.log('\nManual Authentication Setup Instructions:');
    console.log('1. Go to your Supabase dashboard: https://app.supabase.com/');
    console.log('2. Select your project');
    console.log('3. Go to Authentication > Settings');
    console.log('4. Under Email Auth, make sure "Enable Email Signup" is toggled ON');
    console.log('5. For development, you might want to disable email confirmations');
    console.log('6. Save your settings');
    console.log('\nTo create a teacher account manually:');
    console.log('1. Go to Authentication > Users');
    console.log('2. Click "Add User"');
    console.log('3. Enter an email and password');
    console.log('4. Click "Create User"');
    console.log('5. Note the user ID (UUID)');
    console.log('6. Go to Table Editor > teachers');
    console.log('7. Click "Insert row"');
    console.log('8. Fill in:');
    console.log('   - auth_id: The UUID from the created user');
    console.log('   - name: The teacher\'s name');
    console.log('   - email: The same email used for authentication');
    console.log('9. Click "Save"');
  } else if (authOption === '2') {
    console.log('\nRunning teacher account creation script...\n');
    try {
      await executeCommand('node scripts/setup-auth.js');
    } catch (error) {
      console.error('Error running setup-auth.js:', error);
    }
  }
  
  // Step 4: Seed Questions
  console.log('\nStep 4: Seed Initial Questions\n');
  console.log('Would you like to seed the database with initial questions?');
  
  const seedOption = await new Promise(resolve => {
    rl.question('\nSeed the database with questions? (yes/no): ', answer => {
      resolve(answer.toLowerCase() === 'yes');
    });
  });
  
  if (seedOption) {
    console.log('\nRunning question seeding script...\n');
    try {
      await executeCommand('node scripts/seed-questions-commonjs.js');
    } catch (error) {
      console.error('Error running seed-questions-commonjs.js:', error);
    }
  }
  
  // Completion
  console.log('\n=== Setup Process Complete ===\n');
  console.log('Your Supabase configuration should now be ready for use with the Science Assessment App.');
  console.log('You can start the application with:');
  console.log('pnpm dev');
  console.log('\nIf you encounter any issues, please refer to the documentation or contact support.');
  
  rl.close();
}

// Run the setup function
setupSupabase().catch(console.error);
