-- Update Row Level Security (RLS) policies for the test_results table

-- First, let's check if <PERSON><PERSON> is enabled on the test_results table
SELECT tablename, rowsecurity FROM pg_tables WHERE schemaname = 'public' AND tablename = 'test_results';

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Test results accessible by all authenticated users" ON public.test_results;
DROP POLICY IF EXISTS "Test results insertable by authenticated users" ON public.test_results;
DROP POLICY IF EXISTS "Test results updatable by authenticated users" ON public.test_results;
DROP POLICY IF EXISTS "Test results deletable by authenticated users" ON public.test_results;

-- Create policies for the test_results table

-- 1. Allow authenticated users to select (read) test_results
CREATE POLICY "Test results accessible by all authenticated users"
  ON public.test_results FOR SELECT
  TO authenticated
  USING (true);

-- 2. Allow authenticated users to insert test_results
CREATE POLICY "Test results insertable by authenticated users"
  ON public.test_results FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- 3. Allow authenticated users to update test_results
CREATE POLICY "Test results updatable by authenticated users"
  ON public.test_results FOR UPDATE
  TO authenticated
  USING (true);

-- 4. Allow authenticated users to delete test_results
CREATE POLICY "Test results deletable by authenticated users"
  ON public.test_results FOR DELETE
  TO authenticated
  USING (true);

-- Make sure RLS is enabled on the test_results table
ALTER TABLE public.test_results ENABLE ROW LEVEL SECURITY;

-- Verify the policies
SELECT * FROM pg_policies WHERE tablename = 'test_results';
