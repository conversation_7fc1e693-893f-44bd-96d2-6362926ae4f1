# Science Assessment App

A Next.js application for science assessments with student and teacher interfaces.

## Features

- Student pre-test and main-test assessments
- Teacher dashboard for managing students and questions
- AI-generated questions using Google Gemini
- Real-time results and analytics
- Supabase integration for authentication and database

## Tech Stack

- **Frontend**: Next.js, <PERSON>act, <PERSON><PERSON><PERSON> CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL)
- **AI**: Google Gemini API
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm
- Supabase account
- Google Gemini API key

### Local Development

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/science-assessment-app.git
   cd science-assessment-app
   ```

2. Install dependencies:
   ```bash
   pnpm install
   # or
   npm install
   ```

3. Create a `.env.local` file with the following variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_KEY=your_supabase_service_key
   NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
   NODE_ENV=development
   ```

4. Run the development server:
   ```bash
   pnpm dev
   # or
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Deployment

This project is configured for easy deployment to Vercel. See the [Vercel Deployment Guide](vercel-deployment-guide.md) for detailed instructions.

## Project Structure

- `/app` - Next.js app router pages
- `/components` - React components
- `/lib` - Utility functions and API clients
- `/public` - Static assets
- `/scripts` - Setup and database scripts
- `/styles` - Global CSS styles

## License

[MIT](LICENSE)

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.io/)
- [Google Gemini](https://ai.google.dev/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
