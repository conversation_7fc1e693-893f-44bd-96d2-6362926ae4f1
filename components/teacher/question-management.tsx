"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, MoreVertical, PlusCircle, Edit, Trash2, Eye, Loader2, Sparkles } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import {
  getAllQuestions,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  Question
} from "@/lib/api"
import { AIQuestionGenerator } from "./ai-question-generator"

export function QuestionManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showAIGeneratorDialog, setShowAIGeneratorDialog] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null)
  const [questions, setQuestions] = useState<Question[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // New question form state
  const [newQuestion, setNewQuestion] = useState({
    text: "",
    options: ["", "", "", ""],
    correct_answer: "",
    difficulty: "medium" as "easy" | "medium" | "hard",
    explanation: "",
    question_type: "main-test" as "pre-test" | "main-test",
    subject: "science",
    topic: "energy transformation",
    grade_level: 5
  })

  // Fetch questions from the database
  useEffect(() => {
    const fetchQuestions = async () => {
      setIsLoading(true)
      try {
        const data = await getAllQuestions()
        setQuestions(data)
      } catch (error) {
        console.error("Error fetching questions:", error)
        toast.error("Failed to load questions")
      } finally {
        setIsLoading(false)
      }
    }

    fetchQuestions()
  }, [])

  // Filter questions based on search and filters
  const filteredQuestions = questions.filter((question: Question) => {
    const matchesSearch = question.text.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesDifficulty = selectedDifficulty === "all" || question.difficulty === selectedDifficulty

    return matchesSearch && matchesDifficulty
  })

  const handleViewQuestion = (question: Question) => {
    setSelectedQuestion(question)
    setShowViewDialog(true)
  }

  const handleEditQuestion = (question: Question) => {
    setSelectedQuestion({ ...question })
    setShowEditDialog(true)
  }

  const handleDeleteQuestion = (question: Question) => {
    setSelectedQuestion(question)
    setShowDeleteDialog(true)
  }

  const handleAddQuestion = async () => {
    // Validate form
    if (!newQuestion.text) {
      toast.error("Please enter a question text")
      return
    }

    if (newQuestion.options.some((option) => !option)) {
      toast.error("Please fill in all options")
      return
    }

    if (!newQuestion.correct_answer) {
      toast.error("Please select a correct answer")
      return
    }

    if (!newQuestion.explanation) {
      toast.error("Please provide an explanation for the correct answer")
      return
    }

    // Check if the correct answer is one of the options
    if (!newQuestion.options.includes(newQuestion.correct_answer)) {
      toast.error("The correct answer must be one of the options")
      return
    }

    setIsSubmitting(true)

    try {
      // Create question in the database
      const createdQuestion = await createQuestion(newQuestion)

      if (createdQuestion) {
        // Add the new question to the state
        setQuestions([createdQuestion, ...questions])
        setShowAddDialog(false)
        toast.success("Question added successfully")

        // Reset form
        setNewQuestion({
          text: "",
          options: ["", "", "", ""],
          correct_answer: "",
          difficulty: "medium" as "easy" | "medium" | "hard",
          explanation: "",
          question_type: "main-test" as "pre-test" | "main-test",
          subject: "science",
          topic: "energy transformation",
          grade_level: 5
        })
      } else {
        toast.error("Failed to add question")
      }
    } catch (error) {
      console.error("Error adding question:", error)
      toast.error("An error occurred while adding the question")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleUpdateQuestion = async () => {
    if (!selectedQuestion) return

    // Validate form
    if (!selectedQuestion.text) {
      toast.error("Please enter a question text")
      return
    }

    if (selectedQuestion.options.some((option: string) => !option)) {
      toast.error("Please fill in all options")
      return
    }

    if (!selectedQuestion.correct_answer) {
      toast.error("Please select a correct answer")
      return
    }

    if (!selectedQuestion.explanation) {
      toast.error("Please provide an explanation for the correct answer")
      return
    }

    // Check if the correct answer is one of the options
    if (!selectedQuestion.options.includes(selectedQuestion.correct_answer)) {
      toast.error("The correct answer must be one of the options")
      return
    }

    setIsSubmitting(true)

    try {
      // Update question in the database
      const updatedQuestion = await updateQuestion(selectedQuestion.id, {
        text: selectedQuestion.text,
        options: selectedQuestion.options,
        correct_answer: selectedQuestion.correct_answer,
        difficulty: selectedQuestion.difficulty,
        explanation: selectedQuestion.explanation,
        question_type: selectedQuestion.question_type,
        subject: selectedQuestion.subject,
        topic: selectedQuestion.topic,
        grade_level: selectedQuestion.grade_level
      })

      if (updatedQuestion) {
        // Update the question in the state
        const updatedQuestions = questions.map((q: Question) =>
          q.id === selectedQuestion.id ? updatedQuestion : q
        )
        setQuestions(updatedQuestions)
        setShowEditDialog(false)
        toast.success("Question updated successfully")
      } else {
        toast.error("Failed to update question")
      }
    } catch (error) {
      console.error("Error updating question:", error)
      toast.error("An error occurred while updating the question")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleConfirmDelete = async () => {
    if (!selectedQuestion) return

    setIsSubmitting(true)

    try {
      // Delete question from the database
      const success = await deleteQuestion(selectedQuestion.id)

      if (success) {
        // Remove the question from the state
        const updatedQuestions = questions.filter((q: Question) => q.id !== selectedQuestion.id)
        setQuestions(updatedQuestions)
        setShowDeleteDialog(false)
        toast.success("Question deleted successfully")
      } else {
        toast.error("Failed to delete question")
      }
    } catch (error) {
      console.error("Error deleting question:", error)
      toast.error("An error occurred while deleting the question")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAIQuestionsGenerated = (newQuestions: Question[]) => {
    // Add the new questions to the state
    setQuestions([...newQuestions, ...questions])
    setShowAIGeneratorDialog(false)
    toast.success(`${newQuestions.length} questions added successfully`)
  }

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...newQuestion.options]
    newOptions[index] = value
    setNewQuestion({ ...newQuestion, options: newOptions })
  }

  const handleEditOptionChange = (index: number, value: string) => {
    if (!selectedQuestion) return
    const newOptions = [...selectedQuestion.options]
    newOptions[index] = value
    setSelectedQuestion({ ...selectedQuestion, options: newOptions })
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search questions..."
            className="pl-8 border-2 border-purple-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
          <SelectTrigger className="w-[180px] border-2 border-purple-200">
            <SelectValue placeholder="Filter by difficulty" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="easy">Easy</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="hard">Hard</SelectItem>
          </SelectContent>
        </Select>

        <div className="flex gap-2">
          <Button className="bg-purple-500 hover:bg-purple-600" onClick={() => setShowAddDialog(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Question
          </Button>

          <Button
            className="bg-indigo-500 hover:bg-indigo-600"
            onClick={() => setShowAIGeneratorDialog(true)}
          >
            <Sparkles className="mr-2 h-4 w-4" />
            Generate with AI
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border-2 border-green-100 shadow">
          <div className="text-sm text-gray-500">Easy Questions</div>
          <div className="text-2xl font-bold text-green-600">
            {questions.filter((q: Question) => q.difficulty === "easy").length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border-2 border-blue-100 shadow">
          <div className="text-sm text-gray-500">Medium Questions</div>
          <div className="text-2xl font-bold text-blue-600">
            {questions.filter((q: Question) => q.difficulty === "medium").length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border-2 border-purple-100 shadow">
          <div className="text-sm text-gray-500">Hard Questions</div>
          <div className="text-2xl font-bold text-purple-600">
            {questions.filter((q: Question) => q.difficulty === "hard").length}
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex justify-center items-center p-12">
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 animate-spin text-purple-500 mb-4" />
            <p className="text-lg text-gray-600">Loading questions...</p>
          </div>
        </div>
      ) : filteredQuestions.length === 0 ? (
        <div className="text-center p-12 border-2 border-dashed border-gray-300 rounded-lg">
          <p className="text-lg text-gray-600 mb-4">No questions found</p>
          <Button className="bg-purple-500 hover:bg-purple-600" onClick={() => setShowAddDialog(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Your First Question
          </Button>
        </div>
      ) : (
        /* Questions Table */
        <div className="rounded-md border-2 border-purple-100 overflow-hidden">
          <Table>
            <TableHeader className="bg-purple-50">
              <TableRow>
                <TableHead className="w-[80px]">ID</TableHead>
                <TableHead className="w-[50%]">Question</TableHead>
                <TableHead>Difficulty</TableHead>
                <TableHead>Options</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQuestions.map((question: Question) => (
                <TableRow key={question.id}>
                  <TableCell className="font-medium">{question.id.substring(0, 8)}</TableCell>
                  <TableCell className="max-w-md">
                    <div className="truncate">{question.text}</div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={
                        question.difficulty === "easy"
                          ? "bg-green-100 text-green-800 hover:bg-green-100"
                          : question.difficulty === "medium"
                            ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                            : "bg-purple-100 text-purple-800 hover:bg-purple-100"
                      }
                    >
                      {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>{question.options.length}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewQuestion(question)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditQuestion(question)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteQuestion(question)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Question Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Question</DialogTitle>
            <DialogDescription>Create a new question for the Energy Transformation assessment.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="question-text">Question Text</Label>
              <Textarea
                id="question-text"
                placeholder="Enter the question text here..."
                className="min-h-[100px] border-2 border-purple-200 focus:border-purple-400"
                value={newQuestion.text}
                onChange={(e) => setNewQuestion({ ...newQuestion, text: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label>Options</Label>
              <div className="space-y-2">
                {newQuestion.options.map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-700 font-semibold">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <Input
                      placeholder={`Option ${String.fromCharCode(65 + index)}`}
                      className="flex-1 border-2 border-purple-200 focus:border-purple-400"
                      value={option}
                      onChange={(e) => handleOptionChange(index, e.target.value)}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Correct Answer</Label>
              <RadioGroup
                value={newQuestion.correct_answer}
                onValueChange={(value) => setNewQuestion({ ...newQuestion, correct_answer: value })}
              >
                {newQuestion.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <RadioGroupItem value={option} id={`option-${index}`} />
                    <Label htmlFor={`option-${index}`}>{option || `Option ${String.fromCharCode(65 + index)}`}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="difficulty">Difficulty Level</Label>
              <Select
                value={newQuestion.difficulty}
                onValueChange={(value) => setNewQuestion({
                  ...newQuestion,
                  difficulty: value as "easy" | "medium" | "hard"
                })}
              >
                <SelectTrigger className="border-2 border-purple-200">
                  <SelectValue placeholder="Select difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">Easy</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="question-type">Question Type</Label>
              <Select
                value={newQuestion.question_type}
                onValueChange={(value) => setNewQuestion({
                  ...newQuestion,
                  question_type: value as "pre-test" | "main-test"
                })}
              >
                <SelectTrigger className="border-2 border-purple-200">
                  <SelectValue placeholder="Select question type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pre-test">Pre-Test</SelectItem>
                  <SelectItem value="main-test">Main Test</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="explanation">Explanation</Label>
              <Textarea
                id="explanation"
                placeholder="Provide an explanation for the correct answer..."
                className="min-h-[100px] border-2 border-purple-200 focus:border-purple-400"
                value={newQuestion.explanation}
                onChange={(e) => setNewQuestion({ ...newQuestion, explanation: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button
              className="bg-purple-500 hover:bg-purple-600"
              onClick={handleAddQuestion}
              type="button"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                "Add Question"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Question Dialog */}
      {selectedQuestion && (
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Question Details</DialogTitle>
              <DialogDescription>ID: {selectedQuestion.id.substring(0, 8)}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Question Text</h3>
                <div className="p-3 bg-gray-50 rounded-md border">{selectedQuestion.text}</div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Options</h3>
                <div className="space-y-2">
                  {selectedQuestion.options.map((option: string, index: number) => (
                    <div
                      key={index}
                      className={`p-3 rounded-md border ${
                        option === selectedQuestion.correct_answer
                          ? "bg-green-50 border-green-200"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                            option === selectedQuestion.correct_answer
                              ? "bg-green-100 text-green-700"
                              : "bg-gray-100 text-gray-700"
                          }`}
                        >
                          {String.fromCharCode(65 + index)}
                        </div>
                        <div>{option}</div>
                        {option === selectedQuestion.correct_answer && (
                          <Badge className="ml-auto bg-green-100 text-green-800 hover:bg-green-100">Correct</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Explanation</h3>
                <div className="p-3 bg-blue-50 rounded-md border border-blue-200">{selectedQuestion.explanation}</div>
              </div>

              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2">
                  <div className="text-sm font-medium">Difficulty:</div>
                  <Badge
                    className={
                      selectedQuestion.difficulty === "easy"
                        ? "bg-green-100 text-green-800"
                        : selectedQuestion.difficulty === "medium"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-purple-100 text-purple-800"
                    }
                  >
                    {selectedQuestion.difficulty.charAt(0).toUpperCase() + selectedQuestion.difficulty.slice(1)}
                  </Badge>
                </div>

                <div className="flex items-center gap-2">
                  <div className="text-sm font-medium">Type:</div>
                  <Badge className="bg-gray-100 text-gray-800">
                    {selectedQuestion.question_type === "pre-test" ? "Pre-Test" : "Main Test"}
                  </Badge>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                Close
              </Button>
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={() => {
                  setShowViewDialog(false)
                  handleEditQuestion(selectedQuestion)
                }}
                type="button"
              >
                Edit Question
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Question Dialog */}
      {selectedQuestion && (
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Question</DialogTitle>
              <DialogDescription>ID: {selectedQuestion.id}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-question-text">Question Text</Label>
                <Textarea
                  id="edit-question-text"
                  className="min-h-[100px] border-2 border-purple-200 focus:border-purple-400"
                  value={selectedQuestion.text}
                  onChange={(e) => setSelectedQuestion({ ...selectedQuestion, text: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label>Options</Label>
                <div className="space-y-2">
                  {selectedQuestion.options.map((option: string, index: number) => (
                    <div key={index} className="flex gap-2">
                      <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-700 font-semibold">
                        {String.fromCharCode(65 + index)}
                      </div>
                      <Input
                        className="flex-1 border-2 border-purple-200 focus:border-purple-400"
                        value={option}
                        onChange={(e) => handleEditOptionChange(index, e.target.value)}
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Correct Answer</Label>
                <RadioGroup
                  value={selectedQuestion.correct_answer}
                  onValueChange={(value) => setSelectedQuestion({ ...selectedQuestion, correct_answer: value })}
                >
                  {selectedQuestion.options.map((option: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <RadioGroupItem value={option} id={`edit-option-${index}`} />
                      <Label htmlFor={`edit-option-${index}`}>{option}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-difficulty">Difficulty Level</Label>
                <Select
                  value={selectedQuestion.difficulty}
                  onValueChange={(value) => setSelectedQuestion({
                    ...selectedQuestion,
                    difficulty: value as "easy" | "medium" | "hard"
                  })}
                >
                  <SelectTrigger className="border-2 border-purple-200">
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-question-type">Question Type</Label>
                <Select
                  value={selectedQuestion.question_type}
                  onValueChange={(value) => setSelectedQuestion({
                    ...selectedQuestion,
                    question_type: value as "pre-test" | "main-test"
                  })}
                >
                  <SelectTrigger className="border-2 border-purple-200">
                    <SelectValue placeholder="Select question type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pre-test">Pre-Test</SelectItem>
                    <SelectItem value="main-test">Main Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-explanation">Explanation</Label>
                <Textarea
                  id="edit-explanation"
                  className="min-h-[100px] border-2 border-purple-200 focus:border-purple-400"
                  value={selectedQuestion.explanation}
                  onChange={(e) => setSelectedQuestion({ ...selectedQuestion, explanation: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleUpdateQuestion}
                type="button"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Question"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Question Dialog */}
      {selectedQuestion && (
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Delete Question</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this question? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="p-3 bg-gray-50 rounded-md border">
                <p className="font-medium">{selectedQuestion.text}</p>
                <p className="text-sm text-gray-500 mt-2">ID: {selectedQuestion.id}</p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  "Delete"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* AI Question Generator Dialog */}
      <Dialog open={showAIGeneratorDialog} onOpenChange={setShowAIGeneratorDialog}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Generate Questions with AI</DialogTitle>
            <DialogDescription>
              Use AI to generate multiple questions for your assessment. Customize the settings below.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <AIQuestionGenerator
              onQuestionsGenerated={handleAIQuestionsGenerated}
              onClose={() => setShowAIGeneratorDialog(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
