"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, FileText, BarChart3, <PERSON><PERSON>hart, LineChart } from "lucide-react"

export function ReportsDashboard() {
  const [selectedClass, setSelectedClass] = useState<string>("all")
  const [selectedPeriod, setSelectedPeriod] = useState<string>("month")

  return (
    <div className="space-y-6">
      {/* Report Filters */}
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-4">
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-[180px] border-2 border-purple-200">
              <SelectValue placeholder="Select class" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Classes</SelectItem>
              <SelectItem value="5A">Class 5A</SelectItem>
              <SelectItem value="5B">Class 5B</SelectItem>
              <SelectItem value="5C">Class 5C</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px] border-2 border-purple-200">
              <SelectValue placeholder="Time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button className="bg-purple-500 hover:bg-purple-600">
          <Download className="mr-2 h-4 w-4" />
          Export Reports
        </Button>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Average Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-700">78%</div>
            <p className="text-xs text-gray-500 mt-1">+5% from previous period</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Passing Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-700">82%</div>
            <p className="text-xs text-gray-500 mt-1">+3% from previous period</p>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Assessments Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-700">124</div>
            <p className="text-xs text-gray-500 mt-1">+18 from previous period</p>
          </CardContent>
        </Card>
      </div>

      {/* Report Tabs */}
      <Tabs defaultValue="performance">
        <TabsList className="bg-white border-2 border-purple-200">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="questions">Question Analysis</TabsTrigger>
          <TabsTrigger value="progress">Student Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4 pt-4">
          {/* Performance Charts */}
          <Card className="border-2 border-purple-100">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <BarChart3 className="mr-2 h-5 w-5" />
                Score Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-md">
                {/* This would be a chart in a real implementation */}
                <div className="text-center p-4">
                  <BarChart3 className="h-16 w-16 text-purple-300 mx-auto mb-2" />
                  <p className="text-gray-500">Score distribution chart would appear here</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Shows the distribution of student scores across different ranges
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border-2 border-purple-100">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  Level Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center bg-gray-50 rounded-md">
                  {/* This would be a chart in a real implementation */}
                  <div className="text-center p-4">
                    <PieChart className="h-12 w-12 text-purple-300 mx-auto mb-2" />
                    <p className="text-gray-500">Student level distribution</p>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-2 mt-4 text-center text-sm">
                  <div className="p-2 bg-green-50 rounded-md">
                    <div className="font-medium text-green-700">Easy</div>
                    <div>28%</div>
                  </div>
                  <div className="p-2 bg-blue-50 rounded-md">
                    <div className="font-medium text-blue-700">Medium</div>
                    <div>45%</div>
                  </div>
                  <div className="p-2 bg-purple-50 rounded-md">
                    <div className="font-medium text-purple-700">Hard</div>
                    <div>27%</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-purple-100">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center">
                  <LineChart className="mr-2 h-5 w-5" />
                  Performance Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center bg-gray-50 rounded-md">
                  {/* This would be a chart in a real implementation */}
                  <div className="text-center p-4">
                    <LineChart className="h-12 w-12 text-purple-300 mx-auto mb-2" />
                    <p className="text-gray-500">Performance trend over time</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Average Improvement</div>
                    <div className="text-lg font-medium text-green-600">+12%</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Level Upgrades</div>
                    <div className="text-lg font-medium text-purple-600">18 students</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4 pt-4">
          {/* Question Analysis */}
          <Card className="border-2 border-purple-100">
            <CardHeader className="pb-2">
              <CardTitle>Question Difficulty Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-md mb-4">
                {/* This would be a chart in a real implementation */}
                <div className="text-center p-4">
                  <BarChart3 className="h-16 w-16 text-purple-300 mx-auto mb-2" />
                  <p className="text-gray-500">Question difficulty analysis chart would appear here</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-white p-3 rounded-md border border-red-100">
                  <div className="flex justify-between items-center">
                    <div className="font-medium text-red-700">Most Difficult Questions</div>
                    <div className="text-sm text-gray-500">Avg. Success Rate</div>
                  </div>
                  <div className="space-y-2 mt-2">
                    <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                      <div className="text-sm">What energy transformation occurs in a hydroelectric dam?</div>
                      <div className="text-sm font-medium">32%</div>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                      <div className="text-sm">Which energy transformation happens when you strike a match?</div>
                      <div className="text-sm font-medium">38%</div>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                      <div className="text-sm">What energy transformation occurs during photosynthesis?</div>
                      <div className="text-sm font-medium">41%</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-3 rounded-md border border-green-100">
                  <div className="flex justify-between items-center">
                    <div className="font-medium text-green-700">Easiest Questions</div>
                    <div className="text-sm text-gray-500">Avg. Success Rate</div>
                  </div>
                  <div className="space-y-2 mt-2">
                    <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                      <div className="text-sm">What energy transformation occurs in a toaster?</div>
                      <div className="text-sm font-medium">92%</div>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                      <div className="text-sm">Which energy transformation happens in a light bulb?</div>
                      <div className="text-sm font-medium">89%</div>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                      <div className="text-sm">What energy transformation occurs in a loudspeaker?</div>
                      <div className="text-sm font-medium">85%</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-2 border-purple-100">
            <CardHeader className="pb-2">
              <CardTitle>Question Bank Health</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-md border border-purple-100 text-center">
                  <div className="text-sm text-gray-500">Total Questions</div>
                  <div className="text-2xl font-bold text-purple-700">50</div>
                  <div className="text-xs text-gray-500 mt-1">15 easy, 20 medium, 15 hard</div>
                </div>
                <div className="bg-white p-4 rounded-md border border-purple-100 text-center">
                  <div className="text-sm text-gray-500">Question Coverage</div>
                  <div className="text-2xl font-bold text-purple-700">92%</div>
                  <div className="text-xs text-gray-500 mt-1">of curriculum topics covered</div>
                </div>
                <div className="bg-white p-4 rounded-md border border-purple-100 text-center">
                  <div className="text-sm text-gray-500">Recommended</div>
                  <div className="text-2xl font-bold text-orange-500">+5</div>
                  <div className="text-xs text-gray-500 mt-1">more hard questions needed</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4 pt-4">
          {/* Student Progress */}
          <Card className="border-2 border-purple-100">
            <CardHeader className="pb-2">
              <CardTitle>Student Progress Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-md mb-4">
                {/* This would be a chart in a real implementation */}
                <div className="text-center p-4">
                  <LineChart className="h-16 w-16 text-purple-300 mx-auto mb-2" />
                  <p className="text-gray-500">Student progress chart would appear here</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-md border border-green-100 text-center">
                  <div className="text-sm text-gray-500">Improved</div>
                  <div className="text-2xl font-bold text-green-600">68%</div>
                  <div className="text-xs text-gray-500 mt-1">of students improved their scores</div>
                </div>
                <div className="bg-white p-4 rounded-md border border-yellow-100 text-center">
                  <div className="text-sm text-gray-500">Maintained</div>
                  <div className="text-2xl font-bold text-yellow-600">22%</div>
                  <div className="text-xs text-gray-500 mt-1">of students maintained their level</div>
                </div>
                <div className="bg-white p-4 rounded-md border border-red-100 text-center">
                  <div className="text-sm text-gray-500">Declined</div>
                  <div className="text-2xl font-bold text-red-600">10%</div>
                  <div className="text-xs text-gray-500 mt-1">of students showed lower performance</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border-2 border-purple-100">
              <CardHeader className="pb-2">
                <CardTitle>Top Performers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <div className="font-medium">Sophia Chen</div>
                    <div className="text-green-600 font-medium">95%</div>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <div className="font-medium">Maria Garcia</div>
                    <div className="text-green-600 font-medium">92%</div>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <div className="font-medium">Alex Johnson</div>
                    <div className="text-green-600 font-medium">85%</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-purple-100">
              <CardHeader className="pb-2">
                <CardTitle>Needs Improvement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <div className="font-medium">Tyler Johnson</div>
                    <div className="text-red-600 font-medium">58%</div>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                    <div className="font-medium">Jamal Williams</div>
                    <div className="text-red-600 font-medium">65%</div>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                    <div className="font-medium">Carlos Rodriguez</div>
                    <div className="text-yellow-600 font-medium">72%</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Button className="w-full bg-purple-500 hover:bg-purple-600">
            <FileText className="mr-2 h-4 w-4" />
            Generate Detailed Progress Report
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  )
}
