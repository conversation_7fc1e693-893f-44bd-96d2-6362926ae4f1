import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Progress } from "@/components/ui/progress"
import { Loader2, Sparkles, Save, Trash2 } from "lucide-react"
import { toast } from "sonner"
import { processQuestionsAndExplanations, AIGeneratedQuestion } from "@/lib/gemini-ai"
import { createQuestion } from "@/lib/api"

interface AIQuestionGeneratorProps {
  onQuestionsGenerated?: (questions: any[]) => void
  onClose?: () => void
}

export function AIQuestionGenerator({ onQuestionsGenerated, onClose }: AIQuestionGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedQuestions, setGeneratedQuestions] = useState<AIGeneratedQuestion[]>([])
  const [selectedQuestions, setSelectedQuestions] = useState<Record<number, boolean>>({})
  const [isSaving, setIsSaving] = useState(false)

  // Progress tracking
  const [progress, setProgress] = useState(0)
  const [progressStatus, setProgressStatus] = useState("")
  const [currentCount, setCurrentCount] = useState(0)
  const [totalCount, setTotalCount] = useState(0)

  // Generation settings
  const [count, setCount] = useState(5)
  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">("medium")
  const [topic, setTopic] = useState("energy transformation")
  const [subject, setSubject] = useState("science")
  const [questionType, setQuestionType] = useState<"pre-test" | "main-test">("main-test")
  const [gradeLevel, setGradeLevel] = useState(5)

  const handleGenerate = async () => {
    setIsGenerating(true)
    setGeneratedQuestions([])
    setSelectedQuestions({})

    // Reset progress state
    setProgress(0)
    setProgressStatus("Memulai pembuatan pertanyaan...")
    setCurrentCount(0)
    setTotalCount(count)

    // Tampilkan toast untuk memberi tahu pengguna bahwa proses sedang berjalan
    const loadingToast = toast.loading("Sedang membuat pertanyaan... Mohon tunggu (±45 detik)")

    try {
      // Progress callback function
      const handleProgress = (progressPercent: number, current: number, total: number, status?: string) => {
        setProgress(progressPercent)
        setCurrentCount(current)
        setTotalCount(total)
        if (status) {
          setProgressStatus(status)
        }
      }

      // Tambahkan timeout untuk seluruh proses
      const generatePromise = processQuestionsAndExplanations(
        count,
        difficulty,
        topic,
        subject,
        undefined, // customExplanationPrompt
        2, // batchSize
        5000, // delayBetweenBatches
        handleProgress // Pass the progress callback
      );

      // Buat promise timeout untuk membatasi waktu keseluruhan
      const timeoutPromise = new Promise<{questions: AIGeneratedQuestion[], explanations: Record<string, string>}>((_, reject) => {
        setTimeout(() => {
          reject(new Error("Waktu pembuatan pertanyaan habis"));
        }, 90000); // 90 detik timeout (ditingkatkan dari 60 detik)
      });

      // Race antara proses generasi dan timeout
      const result = await Promise.race([generatePromise, timeoutPromise]);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      console.log("Hasil generasi pertanyaan:", result);

      if (result && result.questions && result.questions.length > 0) {
        setGeneratedQuestions(result.questions);

        // Auto-select all generated questions
        const newSelectedQuestions: Record<number, boolean> = {};
        result.questions.forEach((_, index) => {
          newSelectedQuestions[index] = true;
        });
        setSelectedQuestions(newSelectedQuestions);

        // Set progress to 100% when complete
        setProgress(100);
        setProgressStatus("Pembuatan pertanyaan selesai");

        toast.success(`${result.questions.length} pertanyaan berhasil dibuat`);
      } else {
        // Jika tidak ada pertanyaan yang dihasilkan
        console.error("Tidak ada pertanyaan yang berhasil dibuat");
        toast.error("Tidak ada pertanyaan yang berhasil dibuat");
      }
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      console.error("Error generating questions:", error);
      toast.error(`Gagal membuat pertanyaan: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Reset progress state on error
      setProgress(0);
      setProgressStatus("Pembuatan pertanyaan gagal");

      // Coba gunakan pertanyaan contoh sebagai fallback
      try {
        const mockQuestions = [];
        for (let i = 0; i < count; i++) {
          mockQuestions.push({
            text: `Pertanyaan contoh ${i+1} tentang ${topic} (tingkat ${difficulty})`,
            options: ["Pilihan A", "Pilihan B", "Pilihan C", "Pilihan D"],
            correct_answer: "Pilihan A",
            explanation: `Ini adalah penjelasan untuk pertanyaan contoh ${i+1}.`
          });
        }

        setGeneratedQuestions(mockQuestions);

        // Auto-select all generated questions
        const newSelectedQuestions: Record<number, boolean> = {};
        mockQuestions.forEach((_, index) => {
          newSelectedQuestions[index] = true;
        });
        setSelectedQuestions(newSelectedQuestions);

        // Set progress to 100% for fallback questions
        setProgress(100);
        setProgressStatus("Menggunakan pertanyaan contoh");

        toast.warning("Menggunakan pertanyaan contoh karena gagal membuat pertanyaan dengan AI");
      } catch (fallbackError) {
        console.error("Error creating fallback questions:", fallbackError);
      }
    } finally {
      setIsGenerating(false);
    }
  }

  const toggleQuestionSelection = (index: number) => {
    setSelectedQuestions(prev => ({
      ...prev,
      [index]: !prev[index]
    }))
  }

  const handleSaveSelected = async () => {
    const selectedQuestionsList = Object.entries(selectedQuestions)
      .filter(([_, isSelected]) => isSelected)
      .map(([index]) => generatedQuestions[parseInt(index)])

    if (selectedQuestionsList.length === 0) {
      toast.error("Pilih minimal satu pertanyaan untuk disimpan")
      return
    }

    setIsSaving(true)

    try {
      const savedQuestions = []

      for (const question of selectedQuestionsList) {
        const questionData = {
          text: question.text,
          options: question.options,
          correct_answer: question.correct_answer,
          explanation: question.explanation,
          difficulty,
          question_type: questionType,
          subject,
          topic,
          grade_level: gradeLevel
        }

        const savedQuestion = await createQuestion(questionData)
        if (savedQuestion) {
          savedQuestions.push(savedQuestion)
        }
      }

      if (savedQuestions.length > 0) {
        toast.success(`${savedQuestions.length} pertanyaan berhasil disimpan`)
        if (onQuestionsGenerated) {
          onQuestionsGenerated(savedQuestions)
        }
        // Clear generated questions after saving
        setGeneratedQuestions([])
        setSelectedQuestions({})
      } else {
        toast.error("Gagal menyimpan pertanyaan")
      }
    } catch (error) {
      console.error("Error saving questions:", error)
      toast.error("Gagal menyimpan pertanyaan")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="border-2 border-purple-200">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="count">Jumlah Pertanyaan</Label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="count"
                    min={1}
                    max={20}
                    step={1}
                    value={[count]}
                    onValueChange={(value) => setCount(value[0])}
                    className="flex-1"
                  />
                  <span className="w-12 text-center font-medium">{count}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="difficulty">Tingkat Kesulitan</Label>
                <Select value={difficulty} onValueChange={(value: "easy" | "medium" | "hard") => setDifficulty(value)}>
                  <SelectTrigger id="difficulty" className="border-2 border-purple-200">
                    <SelectValue placeholder="Pilih tingkat kesulitan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Mudah</SelectItem>
                    <SelectItem value="medium">Sedang</SelectItem>
                    <SelectItem value="hard">Sulit</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">Topik</Label>
                <Input
                  id="topic"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  className="border-2 border-purple-200"
                  placeholder="Contoh: transformasi energi"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="question-type">Tipe Pertanyaan</Label>
                <Select
                  value={questionType}
                  onValueChange={(value: "pre-test" | "main-test") => setQuestionType(value)}
                >
                  <SelectTrigger id="question-type" className="border-2 border-purple-200">
                    <SelectValue placeholder="Pilih tipe pertanyaan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pre-test">Pre-Test</SelectItem>
                    <SelectItem value="main-test">Main Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="mt-6 space-y-4">
            {isGenerating && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{progressStatus}</span>
                  <span>{currentCount}/{totalCount} pertanyaan</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            <div className="flex justify-center">
              <Button
                onClick={handleGenerate}
                className="bg-purple-500 hover:bg-purple-600 w-full md:w-auto"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Membuat Pertanyaan...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Buat Pertanyaan dengan AI
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {generatedQuestions.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Pertanyaan yang Dihasilkan</h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setGeneratedQuestions([])
                  setSelectedQuestions({})
                }}
                className="border-red-200 text-red-600 hover:bg-red-50"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Hapus Semua
              </Button>
              <Button
                onClick={handleSaveSelected}
                className="bg-green-600 hover:bg-green-700"
                disabled={isSaving || Object.values(selectedQuestions).filter(Boolean).length === 0}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Simpan yang Dipilih
                  </>
                )}
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-sm text-blue-800">
            <h4 className="font-medium mb-2">Informasi Deteksi Kemiripan & Keragaman</h4>
            <p className="mb-2">Sistem secara otomatis meningkatkan keragaman pertanyaan dengan:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Mendeteksi dan mencegah pertanyaan yang terlalu mirip dengan yang sudah ada</li>
              <li>Menggunakan variasi prompt yang berbeda untuk setiap batch pertanyaan</li>
              <li>Merotasi kategori kognitif Bloom (mengingat, memahami, menerapkan, dll)</li>
              <li>Memfokuskan pada subtopik yang berbeda dalam topik yang sama</li>
              <li>Meningkatkan temperature secara bertahap untuk mendorong kreativitas</li>
              <li>Menganalisis pola umum pada pertanyaan yang ada untuk dihindari</li>
            </ul>
            <div className="mt-3 flex flex-wrap gap-2">
              <span className="inline-block px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-md">
                Variasi dari pertanyaan serupa
              </span>
              <span className="inline-block px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-md">
                Variasi dari pertanyaan di database
              </span>
              <span className="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded-md">
                Pertanyaan dengan keragaman tinggi
              </span>
            </div>
          </div>

          <div className="space-y-4">
            {generatedQuestions.map((question, index) => (
              <Card key={index} className={`border-2 ${selectedQuestions[index] ? 'border-green-300 bg-green-50' : 'border-gray-200'}`}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <div className="pt-1">
                      <Switch
                        checked={selectedQuestions[index] || false}
                        onCheckedChange={() => toggleQuestionSelection(index)}
                      />
                    </div>
                    <div className="flex-1 space-y-3">
                      <div>
                        <h4 className="font-medium">
                          {question.text.includes('[Variasi]') ? (
                            <>
                              <span className="inline-block px-2 py-1 mr-2 text-xs bg-yellow-100 text-yellow-800 rounded-md">
                                Variasi dari pertanyaan serupa
                              </span>
                              {question.text.replace('[Variasi] ', '')}
                            </>
                          ) : question.text.includes('[Variasi DB]') ? (
                            <>
                              <span className="inline-block px-2 py-1 mr-2 text-xs bg-orange-100 text-orange-800 rounded-md">
                                Variasi dari pertanyaan di database
                              </span>
                              {question.text.replace('[Variasi DB] ', '')}
                            </>
                          ) : (
                            <>
                              <span className="inline-block px-2 py-1 mr-2 text-xs bg-green-100 text-green-800 rounded-md">
                                Keragaman tinggi
                              </span>
                              {question.text}
                            </>
                          )}
                        </h4>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {question.options.map((option, optIndex) => (
                          <div
                            key={optIndex}
                            className={`p-2 rounded-md ${option === question.correct_answer ? 'bg-green-100 border border-green-300' : 'bg-gray-50 border border-gray-200'}`}
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center text-purple-700 font-semibold text-sm">
                                {String.fromCharCode(65 + optIndex)}
                              </div>
                              <span>{option}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="text-sm text-gray-600">
                        <p><span className="font-medium">Penjelasan:</span> {question.explanation}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {onClose && (
        <div className="flex justify-end mt-6">
          <Button variant="outline" onClick={onClose}>
            Tutup
          </Button>
        </div>
      )}
    </div>
  )
}
