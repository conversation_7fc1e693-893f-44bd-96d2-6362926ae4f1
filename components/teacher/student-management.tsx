"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, MoreVertical, FileText, UserPlus, Download } from "lucide-react"

// Mock student data
const mockStudents = [
  {
    id: "S001",
    name: "<PERSON>",
    grade: "5A",
    level: "medium",
    lastAssessment: "2023-04-15",
    score: 85,
    status: "passed",
  },
  {
    id: "S002",
    name: "<PERSON>",
    grade: "5A",
    level: "hard",
    lastAssessment: "2023-04-14",
    score: 92,
    status: "passed",
  },
  {
    id: "S003",
    name: "Jamal Williams",
    grade: "5B",
    level: "easy",
    lastAssessment: "2023-04-16",
    score: 65,
    status: "failed",
  },
  {
    id: "S004",
    name: "Emma Thompson",
    grade: "5B",
    level: "medium",
    lastAssessment: "2023-04-15",
    score: 78,
    status: "passed",
  },
  {
    id: "S005",
    name: "Carlos Rodriguez",
    grade: "5A",
    level: "medium",
    lastAssessment: "2023-04-13",
    score: 72,
    status: "passed",
  },
  {
    id: "S006",
    name: "Sophia Chen",
    grade: "5C",
    level: "hard",
    lastAssessment: "2023-04-12",
    score: 95,
    status: "passed",
  },
  {
    id: "S007",
    name: "Tyler Johnson",
    grade: "5C",
    level: "easy",
    lastAssessment: "2023-04-16",
    score: 58,
    status: "failed",
  },
]

export function StudentManagement() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGrade, setSelectedGrade] = useState<string>("all")
  const [selectedLevel, setSelectedLevel] = useState<string>("all")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<any>(null)

  // Filter students based on search and filters
  const filteredStudents = mockStudents.filter((student) => {
    const matchesSearch =
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.id.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesGrade = selectedGrade === "all" || student.grade === selectedGrade
    const matchesLevel = selectedLevel === "all" || student.level === selectedLevel

    return matchesSearch && matchesGrade && matchesLevel
  })

  const handleViewDetails = (student: any) => {
    setSelectedStudent(student)
    setShowDetailsDialog(true)
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search students by name or ID..."
            className="pl-8 border-2 border-purple-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select value={selectedGrade} onValueChange={setSelectedGrade}>
          <SelectTrigger className="w-[180px] border-2 border-purple-200">
            <SelectValue placeholder="Filter by class" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Classes</SelectItem>
            <SelectItem value="5A">Class 5A</SelectItem>
            <SelectItem value="5B">Class 5B</SelectItem>
            <SelectItem value="5C">Class 5C</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedLevel} onValueChange={setSelectedLevel}>
          <SelectTrigger className="w-[180px] border-2 border-purple-200">
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="easy">Easy</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="hard">Hard</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border-2 border-purple-100 shadow">
          <div className="text-sm text-gray-500">Total Students</div>
          <div className="text-2xl font-bold text-purple-700">{mockStudents.length}</div>
        </div>
        <div className="bg-white p-4 rounded-lg border-2 border-purple-100 shadow">
          <div className="text-sm text-gray-500">Average Score</div>
          <div className="text-2xl font-bold text-purple-700">
            {Math.round(mockStudents.reduce((sum, student) => sum + student.score, 0) / mockStudents.length)}%
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border-2 border-purple-100 shadow">
          <div className="text-sm text-gray-500">Passing Rate</div>
          <div className="text-2xl font-bold text-purple-700">
            {Math.round((mockStudents.filter((s) => s.status === "passed").length / mockStudents.length) * 100)}%
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border-2 border-purple-100 shadow">
          <div className="text-sm text-gray-500">Need Attention</div>
          <div className="text-2xl font-bold text-red-500">
            {mockStudents.filter((s) => s.status === "failed").length}
          </div>
        </div>
      </div>

      {/* Students Table */}
      <div className="rounded-md border-2 border-purple-100 overflow-hidden">
        <Table>
          <TableHeader className="bg-purple-50">
            <TableRow>
              <TableHead className="w-[100px]">Student ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Class</TableHead>
              <TableHead>Level</TableHead>
              <TableHead>Last Assessment</TableHead>
              <TableHead>Score</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents.map((student) => (
              <TableRow key={student.id}>
                <TableCell className="font-medium">{student.id}</TableCell>
                <TableCell>{student.name}</TableCell>
                <TableCell>{student.grade}</TableCell>
                <TableCell>
                  <Badge
                    className={
                      student.level === "easy"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : student.level === "medium"
                          ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                          : "bg-purple-100 text-purple-800 hover:bg-purple-100"
                    }
                  >
                    {student.level.charAt(0).toUpperCase() + student.level.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell>{student.lastAssessment}</TableCell>
                <TableCell
                  className={
                    student.score >= 80
                      ? "text-green-600 font-medium"
                      : student.score >= 70
                        ? "text-blue-600 font-medium"
                        : "text-red-600 font-medium"
                  }
                >
                  {student.score}%
                </TableCell>
                <TableCell>
                  <Badge
                    className={
                      student.status === "passed"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : "bg-red-100 text-red-800 hover:bg-red-100"
                    }
                  >
                    {student.status.charAt(0).toUpperCase() + student.status.slice(1)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(student)}>
                        <FileText className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Export Results
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" className="border-2 border-purple-200">
          <Download className="mr-2 h-4 w-4" />
          Export All Data
        </Button>
        <Button className="bg-purple-500 hover:bg-purple-600" onClick={() => setShowAddDialog(true)}>
          <UserPlus className="mr-2 h-4 w-4" />
          Add New Student
        </Button>
      </div>

      {/* Add Student Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Student</DialogTitle>
            <DialogDescription>Enter the student details below to add them to the system.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="student-id" className="text-right">
                Student ID
              </Label>
              <Input id="student-id" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Full Name
              </Label>
              <Input id="name" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="class" className="text-right">
                Class
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5A">Class 5A</SelectItem>
                  <SelectItem value="5B">Class 5B</SelectItem>
                  <SelectItem value="5C">Class 5C</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="initial-level" className="text-right">
                Initial Level
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">Easy</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button className="bg-purple-500 hover:bg-purple-600">Add Student</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Student Details Dialog */}
      {selectedStudent && (
        <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Student Details</DialogTitle>
              <DialogDescription>Comprehensive information about {selectedStudent.name}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Student ID</h4>
                  <p className="text-lg">{selectedStudent.id}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Name</h4>
                  <p className="text-lg">{selectedStudent.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Class</h4>
                  <p className="text-lg">{selectedStudent.grade}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Current Level</h4>
                  <p className="text-lg capitalize">{selectedStudent.level}</p>
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Assessment History</h4>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded-md border">
                    <div className="flex justify-between">
                      <span className="font-medium">Energy Transformation</span>
                      <span className="text-gray-500">{selectedStudent.lastAssessment}</span>
                    </div>
                    <div className="mt-2 flex justify-between">
                      <span>
                        Score: <span className="font-medium">{selectedStudent.score}%</span>
                      </span>
                      <Badge
                        className={
                          selectedStudent.status === "passed"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }
                      >
                        {selectedStudent.status.charAt(0).toUpperCase() + selectedStudent.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-md border">
                    <div className="flex justify-between">
                      <span className="font-medium">Simple Machines</span>
                      <span className="text-gray-500">2023-03-20</span>
                    </div>
                    <div className="mt-2 flex justify-between">
                      <span>
                        Score: <span className="font-medium">82%</span>
                      </span>
                      <Badge className="bg-green-100 text-green-800">Passed</Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Progress Notes</h4>
                <p className="text-gray-600 text-sm">
                  {selectedStudent.name} has shown{" "}
                  {selectedStudent.score > 80 ? "excellent" : selectedStudent.score > 70 ? "good" : "some"}{" "}
                  understanding of energy transformation concepts.{" "}
                  {selectedStudent.status === "passed"
                    ? "The student is ready to move on to more complex topics."
                    : "Additional practice with basic concepts is recommended."}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
                Close
              </Button>
              <Button className="bg-purple-500 hover:bg-purple-600">Generate Report</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
