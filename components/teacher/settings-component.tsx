"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Save, AlertTriangle, Loader2, Sparkles } from "lucide-react"
import { getSettings, updateSettings } from "@/lib/api"
import { directUpdateSettings } from "@/lib/direct-db"
import { supabaseAdmin } from "@/lib/supabase"
import { toast } from "sonner"

export function SettingsComponent() {
  const [settings, setSettings] = useState({
    id: "",
    kkm: 70,
    pre_test_questions: 5,
    main_test_questions: 10,
    enable_ai: true,
    ai_model: "gemini",
    ai_prompt: "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
    school_name: "Science Academy",
    school_logo_url: null,
    theme_color: "#4F46E5",
    enable_student_registration: true,
    teacher_name: "Ms. Johnson",
    grade_level: "5",
    subject: "science",
    topic: "Energy Transformation",
    academic_year: "2023-2024",
    auto_grade: true,
    show_explanation: true,
    allow_retry: true,
    max_retries: 3,
    difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
    notify_teacher: true,
    notify_parent: false,
    theme: "default",
    email_template: "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]",
    created_at: new Date().toISOString(),
    updated_at: null
  })

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  // State is now fully managed in the settings object

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const data = await getSettings()
        if (data) {
          // Ensure all required properties exist with defaults
          const updatedSettings = {
            ...settings, // Start with our default settings
            ...data,     // Override with data from the database
            // Ensure these specific properties exist
            ai_model: data.ai_model || "gemini",
            ai_prompt: data.ai_prompt || "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
            enable_ai: typeof data.enable_ai === 'boolean' ? data.enable_ai : true
          }
          setSettings(updatedSettings)
          console.log("Loaded settings:", updatedSettings)
        }
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching settings:", error)
        toast.error("Failed to load settings")
        setIsLoading(false)
      }
    }

    fetchSettings()
  }, [])

  const handleSaveSettings = async () => {
    setIsSaving(true)
    try {
      console.log("Saving settings:", settings)
      console.log("AI Prompt before save:", settings.ai_prompt)

      // Try the regular update first
      let updatedSettings = await updateSettings(settings)

      // If that fails, try the direct update
      if (!updatedSettings) {
        console.log("Regular update failed, trying direct update...")
        updatedSettings = await directUpdateSettings(settings)
      }

      if (updatedSettings) {
        console.log("Settings saved successfully:", updatedSettings)
        console.log("AI Prompt after save:", updatedSettings.ai_prompt)

        // Update the local state with the returned settings to ensure UI is in sync
        setSettings(updatedSettings)

        toast.success("Settings saved successfully")
      } else {
        console.error("Failed to save settings - both update methods returned null")

        // Try a direct database update with a raw SQL query as a last resort
        try {
          console.log("Both update methods failed, trying direct SQL update...");

          // Create a simple update for a few key fields to test if the database is working
          const { data: existingData } = await supabaseAdmin
            .from('settings')
            .select('id')
            .limit(1)
            .single();

          if (existingData && existingData.id) {
            const { data, error } = await supabaseAdmin.rpc('update_settings_direct', {
              settings_id: existingData.id,
              school_name_val: settings.school_name,
              teacher_name_val: settings.teacher_name
            });

            if (!error) {
              console.log("Direct SQL update successful");
              toast.success("Basic settings saved successfully");
              return;
            } else {
              console.error("Direct SQL update failed:", error);
            }
          }
        } catch (sqlError) {
          console.error("Error in direct SQL update:", sqlError);
        }

        // If all methods fail, update the UI to reflect the changes
        // This ensures the user sees their changes even if they're not saved to the database
        toast.warning("Settings updated in UI only - database update failed")
      }
    } catch (error) {
      console.error("Error saving settings:", error)
      toast.error("An error occurred while saving settings")
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
      </div>
    );
  }

  return (
    <Tabs defaultValue="general">
      <TabsList className="bg-white border-2 border-purple-200">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="assessment">Assessment</TabsTrigger>
        <TabsTrigger value="ai" className="flex items-center gap-1">
          AI Settings
          <Sparkles className="h-3 w-3 text-yellow-500" />
        </TabsTrigger>
        <TabsTrigger value="appearance">Appearance</TabsTrigger>
        <TabsTrigger value="notifications">Notifications</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="space-y-4 pt-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle>General Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="school-name">School Name</Label>
                <Input
                  id="school-name"
                  value={settings.school_name || ""}
                  onChange={(e) => setSettings({ ...settings, school_name: e.target.value })}
                  className="border-2 border-purple-200 focus:border-purple-400"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="teacher-name">Teacher Name</Label>
                <Input
                  id="teacher-name"
                  value={settings.teacher_name || ""}
                  onChange={(e) => setSettings({ ...settings, teacher_name: e.target.value })}
                  className="border-2 border-purple-200 focus:border-purple-400"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="grade-level">Grade Level</Label>
                <Select
                  value={settings.grade_level || "5"}
                  onValueChange={(value) => setSettings({ ...settings, grade_level: value })}
                >
                  <SelectTrigger className="border-2 border-purple-200">
                    <SelectValue placeholder="Select grade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">Grade 3</SelectItem>
                    <SelectItem value="4">Grade 4</SelectItem>
                    <SelectItem value="5">Grade 5</SelectItem>
                    <SelectItem value="6">Grade 6</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Select
                  value={settings.subject || "science"}
                  onValueChange={(value) => setSettings({ ...settings, subject: value })}
                >
                  <SelectTrigger className="border-2 border-purple-200">
                    <SelectValue placeholder="Select subject" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="math">Mathematics</SelectItem>
                    <SelectItem value="science">Science</SelectItem>
                    <SelectItem value="english">English</SelectItem>
                    <SelectItem value="social">Social Studies</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="topic">Current Topic</Label>
                <Input
                  id="topic"
                  value={settings.topic || ""}
                  onChange={(e) => setSettings({ ...settings, topic: e.target.value })}
                  className="border-2 border-purple-200 focus:border-purple-400"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="academic-year">Academic Year</Label>
                <Input
                  id="academic-year"
                  value={settings.academic_year || ""}
                  onChange={(e) => setSettings({ ...settings, academic_year: e.target.value })}
                  className="border-2 border-purple-200 focus:border-purple-400"
                />
              </div>
            </div>

            <div className="pt-2">
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleSaveSettings}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save General Settings
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="assessment" className="space-y-4 pt-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle>Assessment Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="kkm" className="text-purple-700">
                  KKM (Minimum Passing Score)
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    id="kkm"
                    type="number"
                    min="0"
                    max="100"
                    value={settings.kkm}
                    onChange={(e) => setSettings({ ...settings, kkm: Number.parseInt(e.target.value) })}
                    className="w-24 border-2 border-purple-200 focus:border-purple-400"
                  />
                  <span className="text-gray-600">%</span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Students must score at least {settings.kkm}% to pass the assessment.
                </p>
              </div>

              <div>
                <Label htmlFor="pre-test-questions" className="text-purple-700">
                  Pre-Test Questions
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    id="pre-test-questions"
                    type="number"
                    value={settings.pre_test_questions}
                    onChange={(e) => setSettings({ ...settings, pre_test_questions: Number.parseInt(e.target.value) })}
                    min="5"
                    max="30"
                    className="w-24 border-2 border-purple-200 focus:border-purple-400"
                  />
                  <span className="text-gray-600">questions</span>
                </div>
              </div>

              <div>
                <Label htmlFor="main-test-questions" className="text-purple-700">
                  Main Test Questions
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    id="main-test-questions"
                    type="number"
                    value={settings.main_test_questions}
                    onChange={(e) => setSettings({ ...settings, main_test_questions: Number.parseInt(e.target.value) })}
                    min="10"
                    max="50"
                    className="w-24 border-2 border-purple-200 focus:border-purple-400"
                  />
                  <span className="text-gray-600">questions</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-purple-700">Difficulty Distribution</Label>
                <div className="grid grid-cols-3 gap-4 mt-1">
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Easy</span>
                      <span>{settings.difficulty_distribution?.easy || 30}%</span>
                    </div>
                    <Slider
                      value={[settings.difficulty_distribution?.easy || 30]}
                      max={100}
                      step={5}
                      className="mt-2"
                      onValueChange={(value) => {
                        const easy = value[0];
                        const medium = settings.difficulty_distribution?.medium || 40;
                        const hard = settings.difficulty_distribution?.hard || 30;

                        // Adjust values to ensure total is 100%
                        const adjustedMedium = Math.round((medium / (medium + hard)) * (100 - easy));
                        const adjustedHard = 100 - easy - adjustedMedium;

                        setSettings({
                          ...settings,
                          difficulty_distribution: {
                            easy,
                            medium: adjustedMedium,
                            hard: adjustedHard
                          }
                        });
                      }}
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Medium</span>
                      <span>{settings.difficulty_distribution?.medium || 40}%</span>
                    </div>
                    <Slider
                      value={[settings.difficulty_distribution?.medium || 40]}
                      max={100}
                      step={5}
                      className="mt-2"
                      onValueChange={(value) => {
                        const medium = value[0];
                        const easy = settings.difficulty_distribution?.easy || 30;
                        const hard = settings.difficulty_distribution?.hard || 30;

                        // Adjust values to ensure total is 100%
                        const adjustedEasy = Math.round((easy / (easy + hard)) * (100 - medium));
                        const adjustedHard = 100 - adjustedEasy - medium;

                        setSettings({
                          ...settings,
                          difficulty_distribution: {
                            easy: adjustedEasy,
                            medium,
                            hard: adjustedHard
                          }
                        });
                      }}
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm">
                      <span>Hard</span>
                      <span>{settings.difficulty_distribution?.hard || 30}%</span>
                    </div>
                    <Slider
                      value={[settings.difficulty_distribution?.hard || 30]}
                      max={100}
                      step={5}
                      className="mt-2"
                      onValueChange={(value) => {
                        const hard = value[0];
                        const easy = settings.difficulty_distribution?.easy || 30;
                        const medium = settings.difficulty_distribution?.medium || 40;

                        // Adjust values to ensure total is 100%
                        const adjustedEasy = Math.round((easy / (easy + medium)) * (100 - hard));
                        const adjustedMedium = 100 - adjustedEasy - hard;

                        setSettings({
                          ...settings,
                          difficulty_distribution: {
                            easy: adjustedEasy,
                            medium: adjustedMedium,
                            hard
                          }
                        });
                      }}
                    />
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  Note: Total should equal 100%. The system will automatically adjust values.
                </p>
              </div>
            </div>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="advanced">
                <AccordionTrigger className="text-purple-700">Advanced Assessment Settings</AccordionTrigger>
                <AccordionContent className="space-y-4 pt-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="auto-grade">Auto-grade assessments</Label>
                      <p className="text-sm text-gray-500">Automatically grade assessments as students complete them</p>
                    </div>
                    <Switch
                      id="auto-grade"
                      checked={settings.auto_grade}
                      onCheckedChange={(checked) => setSettings({ ...settings, auto_grade: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="show-explanation">Show explanations</Label>
                      <p className="text-sm text-gray-500">Show explanations after students answer each question</p>
                    </div>
                    <Switch
                      id="show-explanation"
                      checked={settings.show_explanation}
                      onCheckedChange={(checked) => setSettings({ ...settings, show_explanation: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="allow-retry">Allow retries</Label>
                      <p className="text-sm text-gray-500">Allow students to retry the assessment if they fail</p>
                    </div>
                    <Switch
                      id="allow-retry"
                      checked={settings.allow_retry}
                      onCheckedChange={(checked) => setSettings({ ...settings, allow_retry: checked })}
                    />
                  </div>

                  {settings.allow_retry && (
                    <div>
                      <Label htmlFor="max-retries" className="text-purple-700">
                        Maximum Retries
                      </Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Input
                          id="max-retries"
                          type="number"
                          value={settings.max_retries}
                          onChange={(e) => setSettings({ ...settings, max_retries: Number.parseInt(e.target.value) })}
                          min="1"
                          max="10"
                          className="w-24 border-2 border-purple-200 focus:border-purple-400"
                        />
                        <span className="text-gray-600">attempts</span>
                      </div>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <div className="pt-2">
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleSaveSettings}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Assessment Settings
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="ai" className="space-y-4 pt-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle>AI Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enable-ai">Enable AI-generated explanations</Label>
                <p className="text-sm text-gray-500">
                  When enabled, the system will use AI to generate explanations for questions
                </p>
              </div>
              <Switch
                id="enable-ai"
                checked={settings.enable_ai}
                onCheckedChange={(checked) => setSettings({ ...settings, enable_ai: checked })}
              />
            </div>

            {settings.enable_ai && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="ai-model">AI Model</Label>
                  <Select
                    value={settings.ai_model}
                    onValueChange={(value) => setSettings({ ...settings, ai_model: value })}
                  >
                    <SelectTrigger className="border-2 border-purple-200">
                      <SelectValue placeholder="Select AI model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="gemini">Google Gemini</SelectItem>
                      <SelectItem value="gemini-pro">Google Gemini Pro</SelectItem>
                      <SelectItem value="gemini-1.5-flash">Google Gemini 1.5 Flash</SelectItem>
                      <SelectItem value="openai" disabled>OpenAI (Coming Soon)</SelectItem>
                      <SelectItem value="anthropic" disabled>Anthropic (Coming Soon)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ai-prompt">AI Prompt Template</Label>
                  <Textarea
                    id="ai-prompt"
                    value={settings.ai_prompt || ""}
                    onChange={(e) => {
                      console.log("AI Prompt changed to:", e.target.value);
                      setSettings({
                        ...settings,
                        ai_prompt: e.target.value
                      });
                    }}
                    onBlur={(e) => {
                      console.log("AI Prompt on blur:", e.target.value);
                      console.log("Settings after blur:", { ...settings, ai_prompt: e.target.value });
                    }}
                    className="min-h-[100px] border-2 border-purple-200 focus:border-purple-400"
                  />
                  <p className="text-sm text-gray-500">
                    Use [QUESTION_TEXT] and [CORRECT_ANSWER] as placeholders in your prompt template.
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <h4 className="font-medium text-blue-700 mb-2">AI Explanation Preview</h4>
                  <p className="text-sm text-gray-600">
                    When a student answers a question about energy transformation in a toaster, the AI might respond:
                  </p>
                  <div className="bg-white p-3 rounded border border-blue-100 mt-2 text-sm">
                    <p>
                      The correct answer is "Electrical to heat" because a toaster transforms electrical energy into
                      heat energy. When you plug in a toaster and turn it on, electrical energy flows through the
                      heating elements. These elements have high electrical resistance, which causes them to heat up.
                      This heat energy is what toasts your bread. Some of the electrical energy is also converted to
                      light energy (the red glow you might see) and a tiny bit to sound energy, but the main
                      transformation is from electrical to heat energy.
                    </p>
                  </div>
                </div>
              </>
            )}

            <div className="pt-2">
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleSaveSettings}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save AI Settings
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="appearance" className="space-y-4 pt-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle>Appearance Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <Select
                value={settings.theme || "default"}
                onValueChange={(value) => setSettings({ ...settings, theme: value })}
              >
                <SelectTrigger className="border-2 border-purple-200">
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="space">Space Adventure</SelectItem>
                  <SelectItem value="ocean">Ocean Explorer</SelectItem>
                  <SelectItem value="forest">Forest Discovery</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                className={`border-2 rounded-md p-4 text-center cursor-pointer ${settings.theme === "default" ? "border-purple-500 bg-purple-50" : "border-gray-200"}`}
                onClick={() => setSettings({ ...settings, theme: "default" })}
              >
                <div className="h-20 bg-gradient-to-b from-blue-100 to-purple-100 rounded mb-2"></div>
                <div className="text-sm font-medium">Default</div>
              </div>
              <div
                className={`border-2 rounded-md p-4 text-center cursor-pointer ${settings.theme === "space" ? "border-purple-500 bg-purple-50" : "border-gray-200"}`}
                onClick={() => setSettings({ ...settings, theme: "space" })}
              >
                <div className="h-20 bg-gradient-to-b from-indigo-900 to-purple-900 rounded mb-2"></div>
                <div className="text-sm font-medium">Space Adventure</div>
              </div>
              <div
                className={`border-2 rounded-md p-4 text-center cursor-pointer ${settings.theme === "ocean" ? "border-purple-500 bg-purple-50" : "border-gray-200"}`}
                onClick={() => setSettings({ ...settings, theme: "ocean" })}
              >
                <div className="h-20 bg-gradient-to-b from-blue-300 to-teal-500 rounded mb-2"></div>
                <div className="text-sm font-medium">Ocean Explorer</div>
              </div>
              <div
                className={`border-2 rounded-md p-4 text-center cursor-pointer ${settings.theme === "forest" ? "border-purple-500 bg-purple-50" : "border-gray-200"}`}
                onClick={() => setSettings({ ...settings, theme: "forest" })}
              >
                <div className="h-20 bg-gradient-to-b from-green-200 to-green-500 rounded mb-2"></div>
                <div className="text-sm font-medium">Forest Discovery</div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="theme-color">Theme Color</Label>
              <div className="flex items-center gap-3">
                <Input
                  id="theme-color"
                  type="color"
                  value={settings.theme_color || "#4F46E5"}
                  onChange={(e) => setSettings({ ...settings, theme_color: e.target.value })}
                  className="w-16 h-10 p-1 border-2 border-purple-200 focus:border-purple-400"
                />
                <div className="flex-1">
                  <Input
                    value={settings.theme_color || "#4F46E5"}
                    onChange={(e) => setSettings({ ...settings, theme_color: e.target.value })}
                    className="border-2 border-purple-200 focus:border-purple-400"
                  />
                </div>
              </div>
            </div>

            <div className="pt-2">
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleSaveSettings}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Appearance Settings
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="notifications" className="space-y-4 pt-4">
        <Card className="border-2 border-purple-100">
          <CardHeader className="pb-2">
            <CardTitle>Notification Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notify-teacher">Teacher notifications</Label>
                <p className="text-sm text-gray-500">Receive notifications when students complete assessments</p>
              </div>
              <Switch
                id="notify-teacher"
                checked={settings.notify_teacher}
                onCheckedChange={(checked) => setSettings({ ...settings, notify_teacher: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notify-parent">Parent notifications</Label>
                <p className="text-sm text-gray-500">
                  Send notifications to parents when their child completes an assessment
                </p>
              </div>
              <Switch
                id="notify-parent"
                checked={settings.notify_parent}
                onCheckedChange={(checked) => setSettings({ ...settings, notify_parent: checked })}
              />
            </div>

            {settings.notify_parent && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 flex gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-700 mb-1">Parent Email Configuration Required</h4>
                  <p className="text-sm text-yellow-600">
                    To enable parent notifications, you need to configure parent email addresses in the student
                    management section.
                  </p>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="email-template">Email Notification Template</Label>
              <Textarea
                id="email-template"
                value={settings.email_template || `Dear [RECIPIENT_NAME],

[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.

[PASS_FAIL_MESSAGE]

You can view the detailed results by logging into the assessment platform.

Best regards,
${settings.teacher_name || '[TEACHER_NAME]'}
${settings.school_name || '[SCHOOL_NAME]'}`}
                onChange={(e) => setSettings({ ...settings, email_template: e.target.value })}
                className="min-h-[150px] border-2 border-purple-200 focus:border-purple-400"
              />
            </div>

            <div className="pt-2">
              <Button
                className="bg-purple-500 hover:bg-purple-600"
                onClick={handleSaveSettings}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Notification Settings
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
