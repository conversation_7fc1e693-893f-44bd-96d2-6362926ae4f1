#!/bin/bash

# Login to Nexterm Docker registry
echo "Logging in to Nexterm registry..."
docker login registry.nexterm.net

# Build the Docker image
echo "Building Docker image..."
docker build -t science-assessment-app .

# Tag the image for Nexterm registry
echo "Tagging image for Nexterm registry..."
docker tag science-assessment-app registry.nexterm.net/science-assessment-app:latest

# Push to Nexterm registry
echo "Pushing image to Nexterm registry..."
docker push registry.nexterm.net/science-assessment-app:latest

# Deploy to Nexterm
echo "Deploying to Nexterm..."
docker -H ssh://<EMAIL> run -d \
  --name science-assessment-app \
  -p 3000:3000 \
  --restart unless-stopped \
  registry.nexterm.net/science-assessment-app:latest

echo "Deployment completed!"
