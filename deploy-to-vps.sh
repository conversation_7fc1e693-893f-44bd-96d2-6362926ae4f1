#!/bin/bash

# Science Assessment App VPS Deployment Script
# This script helps deploy the application to a VPS with Docker

# Make script exit on any error
set -e

echo "===== Science Assessment App VPS Deployment ====="
echo "Starting deployment process..."

# Build the Docker image
echo "Building Docker image..."
docker-compose build

# Stop any running containers
echo "Stopping any existing containers..."
docker-compose down || true

# Start the application
echo "Starting the application..."
docker-compose up -d

# Show running containers
echo "Checking running containers..."
docker-compose ps

echo "===== Deployment Complete ====="
echo "The application should now be running at http://your-vps-ip:3000"
echo "If you need to view logs, run: docker-compose logs -f"
