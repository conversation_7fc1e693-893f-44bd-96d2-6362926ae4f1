// Gemini AI integration for generating questions and explanations
//
// Fitur untuk mengurangi kesamaan soal antar batch:
// 1. Variasi prompt - Menggunakan beberapa variasi prompt berbeda untuk setiap batch
// 2. Tracking pertanyaan - Melacak pertanyaan yang sudah dibuat untuk menghindari duplikasi
// 3. Variasi temperature - Meningkatkan temperature untuk batch yang lebih tinggi
// 4. Deteksi kesamaan - Menolak pertanyaan yang terlalu mirip dengan yang sudah ada
// 5. Instruksi eksplisit - Memberikan instruksi ke AI untuk menghindari kesamaan

import { Question, getAllQuestions } from './api';

// Define the structure for AI-generated questions
export interface AIGeneratedQuestion {
  text: string;
  options: string[];
  correct_answer: string;
  explanation: string;
}

// Cache for database questions to avoid repeated fetches
let cachedDatabaseQuestions: Question[] = [];
let lastCacheTime = 0;
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

/**
 * Analyzes a set of questions to identify common patterns and words
 * that should be avoided in new questions to increase diversity
 *
 * @param questions Array of question texts to analyze
 * @returns Array of common words/patterns to avoid
 */
function analyzeCommonPatterns(questions: string[]): string[] {
  if (!questions || questions.length === 0) {
    return [];
  }

  // Combine all questions into a single string and convert to lowercase
  const allText = questions.join(' ').toLowerCase();

  // Remove common stop words and punctuation
  const stopWords = ['dan', 'atau', 'yang', 'di', 'ke', 'dari', 'pada', 'untuk', 'dengan', 'adalah', 'ini', 'itu', 'oleh', 'jika', 'maka', 'tetapi', 'karena', 'sebagai', 'secara', 'dalam', 'akan', 'telah', 'sudah', 'belum', 'tidak', 'bukan', 'dapat', 'bisa', 'harus', 'saat', 'ketika', 'apabila', 'bagaimana', 'mengapa', 'siapa', 'apa', 'mana', 'kapan', 'dimana', 'the', 'a', 'an', 'of', 'to', 'in', 'on', 'at', 'by', 'for', 'with', 'about', 'as', 'into', 'like', 'through', 'after', 'over', 'between', 'out', 'against', 'during', 'without', 'before', 'under', 'around', 'among'];

  // Split into words and count frequency
  const words = allText.replace(/[^\w\s]/g, ' ').split(/\s+/).filter(word =>
    word.length > 3 && !stopWords.includes(word)
  );

  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    wordCounts[word] = (wordCounts[word] || 0) + 1;
  });

  // Sort words by frequency
  const sortedWords = Object.entries(wordCounts)
    .sort((a, b) => b[1] - a[1])
    .filter(([_, count]) => count > Math.max(3, questions.length / 4)) // Only include words that appear frequently
    .map(([word]) => word);

  // Return top 10 most common words
  return sortedWords.slice(0, 10);
}

// Function to fetch all questions from the database with caching
export async function getExistingQuestionsFromDatabase(): Promise<Question[]> {
  const now = Date.now();

  // If cache is valid, use it
  if (cachedDatabaseQuestions.length > 0 && (now - lastCacheTime) < CACHE_EXPIRY_MS) {
    console.log(`Using cached database questions (${cachedDatabaseQuestions.length} questions)`);
    return cachedDatabaseQuestions;
  }

  // Otherwise fetch from database
  try {
    console.log('Fetching questions from database...');
    const questions = await getAllQuestions();

    // Update cache
    cachedDatabaseQuestions = questions;
    lastCacheTime = now;

    console.log(`Fetched ${questions.length} questions from database`);
    return questions;
  } catch (error) {
    console.error('Error fetching questions from database:', error);
    // If there's an error but we have cached data, return that
    if (cachedDatabaseQuestions.length > 0) {
      console.log(`Using cached database questions despite error (${cachedDatabaseQuestions.length} questions)`);
      return cachedDatabaseQuestions;
    }
    // Otherwise return empty array
    return [];
  }
}

// Define the structure for batch processing results
export interface BatchProcessingResult {
  questions: AIGeneratedQuestion[];
  explanations: Record<string, string>;
}

// Function to generate questions based on student level
// Helper function to handle rate limiting with exponential backoff
async function callWithRetry<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 5, // Increased from 3 to 5
  initialDelay: number = 8000, // Increased initial delay to 8 seconds
  maxDelay: number = 120000 // Maximum delay of 2 minutes
): Promise<T> {
  let retries = 0;
  let delay = initialDelay;

  while (true) {
    try {
      return await apiCall();
    } catch (error: any) {
      // Enhanced check for rate limit errors
      const isRateLimit =
        error.message?.includes('quota') ||
        error.message?.includes('rate limit') ||
        error.message?.includes('429') ||
        error.message?.includes('Too Many Requests') ||
        error.message?.includes('Resource exhausted') ||
        error.message?.includes('limit exceeded') ||
        error.message?.includes('try again') ||
        error.message?.includes('temporarily unavailable');

      // If we've used all retries or it's not a rate limit error, throw
      if (retries >= maxRetries || !isRateLimit) {
        console.error(`API call failed after ${retries} retries:`, error);
        throw error;
      }

      // Log the retry attempt
      console.log(`Rate limit hit. Retrying in ${delay}ms... (Attempt ${retries + 1}/${maxRetries})`);

      // Wait for the delay period
      await new Promise(resolve => setTimeout(resolve, delay));

      // Increase the delay for next retry (exponential backoff) with a maximum cap
      delay = Math.min(delay * 2, maxDelay);
      retries++;

      // Add a small random jitter to avoid synchronized retries
      const jitter = Math.floor(Math.random() * 2000); // 0-2000ms jitter
      await new Promise(resolve => setTimeout(resolve, jitter));
      console.log(`Added jitter of ${jitter}ms to avoid synchronized retries`);
    }
  }
}

export async function generateQuestionWithGemini(
  level: 'easy' | 'medium' | 'hard',
  topic: string = 'energy transformation',
  subject: string = 'science',
  batchIndex: number = 0,
  previousQuestions: string[] = []
): Promise<AIGeneratedQuestion | null> {
  try {
    // Check if API key is available
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    console.log('Gemini API key available:', !!apiKey);

    if (!apiKey || apiKey === 'your_gemini_api_key_here') {
      console.warn('Gemini API key not found or not set. Using mock response.');
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Return a mock response based on the level
      return getMockQuestionResponse(level);
    }

    // Dapatkan kata-kata umum yang perlu dihindari jika belum ada
    let commonWordsToAvoid: string[] = [];
    try {
      // Coba dapatkan pertanyaan dari database jika belum dilakukan
      if (!cachedDatabaseQuestions.length) {
        const dbQuestions = await getExistingQuestionsFromDatabase();
        commonWordsToAvoid = analyzeCommonPatterns(dbQuestions.map(q => q.text));
      } else {
        commonWordsToAvoid = analyzeCommonPatterns(cachedDatabaseQuestions.map(q => q.text));
      }
    } catch (error) {
      console.error('Error getting common words to avoid:', error);
      // Lanjutkan meskipun ada error
    }

    // Construct the prompt based on the student's level, topic, previous questions, and common words to avoid
    const prompt = constructQuestionPrompt(level, topic, subject, batchIndex, previousQuestions, commonWordsToAvoid);

    // Always use gemini-2.0-flash model without calling settings
    const modelEndpoint = 'gemini-2.0-flash';

    console.log(`Calling Gemini API (${modelEndpoint}) with prompt:`, prompt.substring(0, 100) + '...');

    // Variasikan temperature berdasarkan batch index untuk meningkatkan keragaman
    // Semakin tinggi batch index, semakin tinggi temperature (dalam batas wajar)
    // Meningkatkan temperature settings untuk mendorong keragaman yang lebih besar
    const baseTemperature = 0.75; // Ditingkatkan dari 0.7
    const temperatureIncrement = 0.07; // Ditingkatkan dari 0.05
    const maxTemperature = 0.98; // Ditingkatkan dari 0.95

    // Tambahkan juga variasi temperature berdasarkan posisi pertanyaan dalam batch
    // untuk meningkatkan keragaman dalam satu batch yang sama
    const questionPositionVariation = previousQuestions.length * 0.02; // Tambahkan variasi kecil berdasarkan jumlah pertanyaan sebelumnya

    const temperature = Math.min(
      baseTemperature + (batchIndex * temperatureIncrement) + questionPositionVariation,
      maxTemperature
    );

    console.log(`Using temperature: ${temperature} for batch ${batchIndex} (question position variation: +${questionPositionVariation})`);

    // Call the Gemini API with retry logic
    const response = await callWithRetry(async () => {
      const res = await fetch(`https://generativelanguage.googleapis.com/v1/models/${modelEndpoint}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': apiKey
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: temperature,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });

      if (!res.ok) {
        const errorText = await res.text().catch(() => "No error details available");
        console.error(`Gemini API error response: ${errorText}`);
        throw new Error(`Gemini API error: ${res.status} ${res.statusText} - ${errorText}`);
      }

      return res;
    });

    const data = await response.json();

    // Extract the generated text
    // Handle both v1 and v1beta response formats
    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text ||
                         data.candidates?.[0]?.text ||
                         data.text ||
                         "";

    // Parse the JSON response
    try {
      console.log('Attempting to parse Gemini response...');
      console.log('Raw response text:', generatedText);

      // Try multiple approaches to extract JSON

      // Approach 1: Try to find JSON object with a more robust regex that matches from the first { to the last }
      const jsonMatch = generatedText.match(/\{[\s\S]*?\}(?=\s*$)/);

      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        console.log('Approach 1 - Found JSON string:', jsonStr.substring(0, 100) + (jsonStr.length > 100 ? '...' : ''));

        try {
          const parsedQuestion = JSON.parse(jsonStr);

          // Validate the response structure
          if (
            parsedQuestion.text &&
            Array.isArray(parsedQuestion.options) &&
            parsedQuestion.options.length === 4 &&
            parsedQuestion.correct_answer &&
            parsedQuestion.explanation
          ) {
            console.log('Successfully parsed valid question JSON with approach 1');
            return parsedQuestion as AIGeneratedQuestion;
          } else {
            console.warn('JSON parsed but missing required fields. Structure:', JSON.stringify(parsedQuestion, null, 2).substring(0, 200));
          }
        } catch (jsonError) {
          console.error('Error parsing JSON string with approach 1:', jsonError);
        }
      } else {
        console.log('No JSON match found with approach 1');
      }

      // Approach 2: Look for anything that looks like a JSON object (more lenient)
      const alternativeMatch = generatedText.match(/\{[\s\S]*\}/);
      if (alternativeMatch) {
        const jsonStr = alternativeMatch[0];
        console.log('Approach 2 - Found JSON string:', jsonStr.substring(0, 100) + (jsonStr.length > 100 ? '...' : ''));

        try {
          const parsedQuestion = JSON.parse(jsonStr);

          // Validate the response structure
          if (
            parsedQuestion.text &&
            Array.isArray(parsedQuestion.options) &&
            parsedQuestion.options.length === 4 &&
            parsedQuestion.correct_answer &&
            parsedQuestion.explanation
          ) {
            console.log('Successfully parsed valid question JSON with approach 2');
            return parsedQuestion as AIGeneratedQuestion;
          } else {
            console.warn('JSON parsed with approach 2 but missing required fields. Structure:', JSON.stringify(parsedQuestion, null, 2).substring(0, 200));
          }
        } catch (altError) {
          console.error('Error parsing JSON string with approach 2:', altError);
        }
      } else {
        console.log('No JSON match found with approach 2');
      }

      // Approach 3: Try to manually clean the response and extract JSON
      try {
        // Find the first { and last } in the text
        const startIndex = generatedText.indexOf('{');
        const endIndex = generatedText.lastIndexOf('}');

        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          const jsonStr = generatedText.substring(startIndex, endIndex + 1);
          console.log('Approach 3 - Extracted JSON string:', jsonStr.substring(0, 100) + (jsonStr.length > 100 ? '...' : ''));

          const parsedQuestion = JSON.parse(jsonStr);

          // Validate the response structure
          if (
            parsedQuestion.text &&
            Array.isArray(parsedQuestion.options) &&
            parsedQuestion.options.length === 4 &&
            parsedQuestion.correct_answer &&
            parsedQuestion.explanation
          ) {
            console.log('Successfully parsed valid question JSON with approach 3');
            return parsedQuestion as AIGeneratedQuestion;
          } else {
            console.warn('JSON parsed with approach 3 but missing required fields. Structure:', JSON.stringify(parsedQuestion, null, 2).substring(0, 200));
          }
        } else {
          console.log('Could not find valid JSON start and end with approach 3');
        }
      } catch (approach3Error) {
        console.error('Error parsing JSON string with approach 3:', approach3Error);
      }

      console.warn('All JSON parsing approaches failed. Using mock response.');
      console.log('Full raw response that could not be parsed:', generatedText);

      console.warn('Invalid response format from Gemini API. Using mock response.');
      return getMockQuestionResponse(level);
    } catch (parseError) {
      console.error('Error parsing Gemini API response:', parseError);
      console.log('Raw response text that caused the error:', generatedText);
      return getMockQuestionResponse(level);
    }
  } catch (error) {
    console.error('Error generating question with Gemini:', error);
    // Fallback to mock response
    return getMockQuestionResponse(level);
  }
}

// Function to generate explanations for student answers
export async function generateExplanationWithGemini(
  question: string,
  studentAnswer: string,
  correctAnswer: string,
  isCorrect: boolean,
  level: 'easy' | 'medium' | 'hard',
  customExplanationPrompt?: string
): Promise<string | null> {
  try {
    // Check if API key is available
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    if (!apiKey || apiKey === 'your_gemini_api_key_here') {
      console.warn('Gemini API key not found or not set. Using mock response.');
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Return a mock explanation
      return getMockExplanationResponse(question, studentAnswer, correctAnswer, isCorrect, level);
    }

    // Construct the prompt for explanation
    let prompt;
    if (customExplanationPrompt) {
      // Use custom prompt template if provided
      prompt = customExplanationPrompt
        .replace('[QUESTION_TEXT]', question)
        .replace('[CORRECT_ANSWER]', correctAnswer)
        .replace('[STUDENT_ANSWER]', studentAnswer)
        .replace('[LEVEL]', level)
        .replace('[IS_CORRECT]', isCorrect ? 'true' : 'false');
    } else {
      // Use default prompt
      prompt = constructExplanationPrompt(question, studentAnswer, correctAnswer, isCorrect, level);
    }

    // Always use gemini-2.0-flash model without calling settings
    const modelEndpoint = 'gemini-2.0-flash';

    console.log(`Calling Gemini API (${modelEndpoint}) for explanation with prompt:`, prompt.substring(0, 100) + '...');

    // Call the Gemini API with retry logic
    const response = await callWithRetry(async () => {
      const res = await fetch(`https://generativelanguage.googleapis.com/v1/models/${modelEndpoint}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': apiKey
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });

      if (!res.ok) {
        const errorText = await res.text().catch(() => "No error details available");
        console.error(`Gemini API error response: ${errorText}`);
        throw new Error(`Gemini API error: ${res.status} ${res.statusText} - ${errorText}`);
      }

      return res;
    });

    const data = await response.json();

    // Extract the generated text
    // Handle both v1 and v1beta response formats
    const explanation = data.candidates?.[0]?.content?.parts?.[0]?.text ||
                       data.candidates?.[0]?.text ||
                       data.text ||
                       "";

    return explanation;
  } catch (error) {
    console.error('Error generating explanation with Gemini:', error);
    // Fallback to mock response
    return getMockExplanationResponse(question, studentAnswer, correctAnswer, isCorrect, level);
  }
}

// Helper function to construct question generation prompt
function constructQuestionPrompt(
  level: 'easy' | 'medium' | 'hard',
  topic: string = 'energy transformation',
  subject: string = 'science',
  batchIndex: number = 0,
  previousQuestions: string[] = [],
  commonWords: string[] = [] // Tambahkan parameter untuk kata-kata umum yang perlu dihindari
): string {
  const difficultyDescription = {
    easy: 'konsep sederhana dengan jawaban yang mudah dipahami',
    medium: 'konsep menengah yang membutuhkan pemahaman tertentu',
    hard: 'konsep lanjutan yang membutuhkan pemahaman dan penerapan yang lebih mendalam'
  };

  // Definisikan kategori kognitif Bloom untuk rotasi
  const bloomCategories = [
    {
      name: "mengingat",
      instruction: "menguji kemampuan MENGINGAT fakta dan konsep dasar"
    },
    {
      name: "memahami",
      instruction: "menguji kemampuan MEMAHAMI dan menjelaskan ide atau konsep"
    },
    {
      name: "menerapkan",
      instruction: "menguji kemampuan MENERAPKAN pengetahuan dalam situasi baru"
    },
    {
      name: "menganalisis",
      instruction: "menguji kemampuan MENGANALISIS dengan memecah informasi menjadi bagian-bagian"
    },
    {
      name: "mengevaluasi",
      instruction: "menguji kemampuan MENGEVALUASI dengan membuat penilaian berdasarkan kriteria"
    },
    {
      name: "mencipta",
      instruction: "menguji kemampuan MENCIPTA dengan menggabungkan elemen menjadi pola baru"
    }
  ];

  // Pilih kategori Bloom berdasarkan kombinasi batch index dan previousQuestions.length
  const bloomIndex = (batchIndex + previousQuestions.length) % bloomCategories.length;
  const selectedBloomCategory = bloomCategories[bloomIndex];

  // Definisikan subtopik untuk topik umum
  const subtopics: Record<string, string[]> = {
    "energy transformation": [
      "transformasi energi potensial ke kinetik",
      "transformasi energi listrik ke cahaya",
      "transformasi energi kimia ke listrik",
      "transformasi energi panas ke mekanik",
      "transformasi energi cahaya ke listrik",
      "transformasi energi dalam ekosistem",
      "transformasi energi dalam tubuh manusia"
    ],
    "force and motion": [
      "hukum gerak Newton",
      "gaya gravitasi",
      "gaya gesek",
      "momentum dan impuls",
      "gaya magnet",
      "gaya listrik",
      "gaya apung"
    ],
    "matter": [
      "perubahan wujud zat",
      "sifat fisika dan kimia",
      "unsur dan senyawa",
      "campuran dan larutan",
      "atom dan molekul",
      "reaksi kimia sederhana",
      "asam dan basa"
    ]
  };

  // Pilih subtopik jika tersedia untuk topik yang dipilih
  let selectedSubtopic = topic;
  if (subtopics[topic.toLowerCase()]) {
    const subtopicIndex = (batchIndex + previousQuestions.length) % subtopics[topic.toLowerCase()].length;
    selectedSubtopic = subtopics[topic.toLowerCase()][subtopicIndex];
    console.log(`Using subtopic: ${selectedSubtopic} for topic ${topic}`);
  }

  // Variasi prompt yang lebih beragam berdasarkan batch index untuk meningkatkan keragaman
  const promptVariations = [
    `Hasilkan pertanyaan pilihan ganda yang unik dengan tingkat kesulitan ${level} tentang ${selectedSubtopic} dalam mata pelajaran ${subject} untuk kelas SD, ${selectedBloomCategory.instruction}.`,

    `Buatkan pertanyaan pilihan ganda yang fokus pada APLIKASI PRAKTIS dengan tingkat kesulitan ${level} tentang ${selectedSubtopic} dalam bidang ${subject} untuk siswa SD, ${selectedBloomCategory.instruction}.`,

    `Ciptakan pertanyaan pilihan ganda yang menekankan KONSEP TEORITIS dengan tingkat kesulitan ${level} mengenai ${selectedSubtopic} dalam pelajaran ${subject} untuk anak SD, ${selectedBloomCategory.instruction}.`,

    `Buat pertanyaan pilihan ganda yang menggunakan CONTOH KEHIDUPAN SEHARI-HARI dengan tingkat kesulitan ${level} seputar ${selectedSubtopic} dalam mata pelajaran ${subject} untuk siswa SD, ${selectedBloomCategory.instruction}.`,

    `Rancang pertanyaan pilihan ganda yang menguji KEMAMPUAN ANALISIS dengan tingkat kesulitan ${level} tentang ${selectedSubtopic} dalam pelajaran ${subject} untuk siswa SD, ${selectedBloomCategory.instruction}.`,

    `Susun pertanyaan pilihan ganda yang mengaitkan ${selectedSubtopic} dengan TEKNOLOGI MODERN dengan tingkat kesulitan ${level} dalam mata pelajaran ${subject} untuk siswa SD, ${selectedBloomCategory.instruction}.`,

    `Kembangkan pertanyaan pilihan ganda yang menghubungkan ${selectedSubtopic} dengan FENOMENA ALAM dengan tingkat kesulitan ${level} dalam pelajaran ${subject} untuk siswa SD, ${selectedBloomCategory.instruction}.`,

    `Formulasikan pertanyaan pilihan ganda yang meminta siswa MEMBANDINGKAN DAN MEMBEDAKAN konsep dalam ${selectedSubtopic} dengan tingkat kesulitan ${level} untuk mata pelajaran ${subject} SD, ${selectedBloomCategory.instruction}.`
  ];

  // Pilih variasi prompt berdasarkan kombinasi batch index dan previousQuestions.length
  // untuk memastikan variasi maksimal
  const promptIndex = (batchIndex * 3 + previousQuestions.length) % promptVariations.length;
  const promptVariation = promptVariations[promptIndex];

  console.log(`Using prompt variation #${promptIndex+1} with Bloom category "${selectedBloomCategory.name}" for batch ${batchIndex}`);

  // Tambahkan instruksi untuk menghindari kesamaan dengan pertanyaan sebelumnya
  let avoidDuplicationText = '';
  if (previousQuestions.length > 0) {
    avoidDuplicationText = `
PENTING: Hindari membuat pertanyaan yang mirip dengan pertanyaan-pertanyaan berikut:
${previousQuestions.map(q => `- "${q}"`).join('\n')}

Pastikan pertanyaan yang Anda buat memiliki sudut pandang, konteks, atau aspek yang BERBEDA dari pertanyaan-pertanyaan di atas.`;
  }

  // Instruksi eksplisit untuk keragaman
  // Tambahkan kata-kata umum yang perlu dihindari jika tersedia
  const commonWordsToAvoid = commonWords && commonWords.length > 0 ?
    `\nHindari menggunakan kata-kata dan frasa berikut yang terlalu sering muncul dalam pertanyaan yang sudah ada:
${commonWords.map(word => `- "${word}"`).join('\n')}` : '';

  const diversityInstruction = `
SANGAT PENTING UNTUK KERAGAMAN PERTANYAAN:
- Gunakan sudut pandang yang BERBEDA dari pertanyaan umum tentang topik ini
- Fokus pada aspek yang JARANG DIBAHAS dari ${selectedSubtopic}
- Gunakan konteks atau skenario yang UNIK dan SPESIFIK
- Hindari menggunakan kata kunci atau frasa yang sama dengan pertanyaan umum
- Pastikan pertanyaan ini BERBEDA SECARA SIGNIFIKAN dari pertanyaan lain tentang ${topic}
- Gunakan pendekatan yang tidak biasa atau sudut pandang yang berbeda
- Jika memungkinkan, kaitkan dengan konteks yang tidak terduga${commonWordsToAvoid}
`;

  // Use a clear, structured prompt with explicit JSON response format
  return `${promptVariation}
Pertanyaan harus menguji ${difficultyDescription[level]}.
Sediakan 4 pilihan jawaban dengan tepat satu jawaban yang benar.${avoidDuplicationText}

${diversityInstruction}

SANGAT PENTING: Berikan respons HANYA dalam format JSON berikut, tanpa teks tambahan sebelum atau sesudah JSON.
Jangan menambahkan komentar, penjelasan, atau teks apapun di luar struktur JSON.
Jangan menggunakan tanda kutip tambahan di sekitar JSON.
Pastikan JSON valid dan dapat di-parse.

{
  "text": "Teks pertanyaan yang unik dan berbeda dari pertanyaan lain",
  "options": ["Pilihan A", "Pilihan B", "Pilihan C", "Pilihan D"],
  "correct_answer": "Teks pilihan jawaban yang benar",
  "explanation": "Penjelasan detail mengapa jawaban benar dan mengapa pilihan lain salah"
}`;
}

// Helper function to construct explanation prompt
function constructExplanationPrompt(
  question: string,
  studentAnswer: string,
  correctAnswer: string,
  isCorrect: boolean,
  level: 'easy' | 'medium' | 'hard'
): string {
  const responseType = isCorrect ? 'positive reinforcement' : 'encouraging correction';

  return `Anda adalah guru sains yang membantu untuk siswa SD.
  ${isCorrect ? 'Siswa menjawab dengan benar!' : 'Siswa menjawab dengan tidak tepat.'}

  Pertanyaan: "${question}"
  Jawaban siswa: "${studentAnswer}"
  Jawaban yang benar: "${correctAnswer}"

  Berikan penjelasan tingkat ${level} yang memberikan ${responseType}.
  Untuk siswa tingkat ${level}, buat penjelasan Anda ${level === 'easy' ? 'sangat sederhana dan memotivasi' :
    level === 'medium' ? 'cukup detail dengan beberapa istilah ilmiah' :
    'lebih detail dengan terminologi ilmiah yang tepat namun tetap sesuai untuk siswa SD'}.

  Batasi respons Anda hingga 150 kata dan buat menarik untuk siswa SD.`;
}

// Mock response functions for development/testing
export function getMockQuestionResponse(level: 'easy' | 'medium' | 'hard'): AIGeneratedQuestion {
  const questions = {
    easy: {
      text: "Apa yang terjadi pada energi kimia dalam baterai saat kamu menyalakan senter?",
      options: [
        "Energi menghilang",
        "Energi berubah menjadi cahaya dan panas",
        "Energi tetap sama",
        "Energi berubah menjadi energi suara"
      ],
      correct_answer: "Energi berubah menjadi cahaya dan panas",
      explanation: "Ketika kamu menyalakan senter, energi kimia yang tersimpan dalam baterai berubah menjadi energi listrik, yang kemudian berubah menjadi energi cahaya dan sedikit energi panas. Ini adalah contoh perubahan energi dari satu bentuk ke bentuk lainnya."
    },
    medium: {
      text: "Perubahan energi apa yang terjadi ketika kamu menggosok-gosokkan tanganmu?",
      options: [
        "Energi kimia menjadi energi panas",
        "Energi mekanik menjadi energi panas",
        "Energi listrik menjadi energi panas",
        "Energi cahaya menjadi energi panas"
      ],
      correct_answer: "Energi mekanik menjadi energi panas",
      explanation: "Saat kamu menggosok-gosokkan tanganmu, kamu menggunakan energi mekanik (gerakan) yang kemudian berubah menjadi energi panas karena gesekan. Gesekan antara kedua tanganmu menghasilkan panas, yang merupakan contoh perubahan energi mekanik menjadi energi panas."
    },
    hard: {
      text: "Dalam panel surya, perubahan energi apa yang terjadi dan mengapa ini penting untuk lingkungan?",
      options: [
        "Energi panas menjadi energi listrik, penting karena tidak menghasilkan polusi",
        "Energi cahaya menjadi energi listrik, penting karena merupakan sumber energi terbarukan",
        "Energi kimia menjadi energi listrik, penting karena mudah disimpan",
        "Energi mekanik menjadi energi listrik, penting karena efisien"
      ],
      correct_answer: "Energi cahaya menjadi energi listrik, penting karena merupakan sumber energi terbarukan",
      explanation: "Panel surya mengubah energi cahaya dari matahari menjadi energi listrik melalui efek fotovoltaik. Ini sangat penting untuk lingkungan karena energi matahari adalah sumber energi terbarukan yang tidak akan habis, tidak seperti bahan bakar fosil. Selain itu, proses ini tidak menghasilkan polusi atau gas rumah kaca, sehingga lebih ramah lingkungan."
    }
  };

  return questions[level];
}

function getMockExplanationResponse(
  _question: string, // Prefixed with underscore to indicate it's not used
  studentAnswer: string,
  correctAnswer: string,
  isCorrect: boolean,
  level: 'easy' | 'medium' | 'hard'
): string {
  if (isCorrect) {
    if (level === 'easy') {
      return `Hebat! Kamu benar sekali. ${correctAnswer} adalah jawaban yang tepat. Energi memang bisa berubah bentuk, seperti yang terjadi dalam contoh ini. Kamu sudah memahami konsep dasar perubahan energi dengan baik!`;
    } else if (level === 'medium') {
      return `Bagus sekali! Jawabanmu "${correctAnswer}" tepat. Kamu telah menunjukkan pemahaman yang baik tentang konsep perubahan energi. Dalam ilmu sains, kita menyebut ini sebagai "transformasi energi" - energi tidak pernah hilang, hanya berubah bentuk. Teruslah belajar dengan semangat!`;
    } else {
      return `Luar biasa! Jawabanmu "${correctAnswer}" sangat tepat. Kamu menunjukkan pemahaman yang mendalam tentang konsep transformasi energi. Prinsip konservasi energi menyatakan bahwa energi tidak dapat diciptakan atau dimusnahkan, hanya dapat berubah bentuk. Analisismu menunjukkan kemampuan berpikir kritis yang baik!`;
    }
  } else {
    if (level === 'easy') {
      return `Jawaban yang benar adalah "${correctAnswer}". Jangan khawatir jika kamu belum mendapatkannya dengan benar. Energi bisa berubah dari satu bentuk ke bentuk lain. Mari kita pelajari bersama-sama. Kamu pasti bisa!`;
    } else if (level === 'medium') {
      return `Jawabanmu "${studentAnswer}" tidak tepat. Jawaban yang benar adalah "${correctAnswer}". Dalam transformasi energi, energi berubah bentuk tetapi jumlah totalnya tetap sama. Cobalah pikirkan contoh lain dari perubahan energi dalam kehidupan sehari-hari. Kamu hampir memahaminya!`;
    } else {
      return `Jawabanmu "${studentAnswer}" kurang tepat. Jawaban yang benar adalah "${correctAnswer}". Dalam ilmu fisika, kita mempelajari bahwa energi mengikuti hukum kekekalan energi - energi tidak dapat diciptakan atau dimusnahkan. Perhatikan proses perubahan bentuk energi yang terjadi dan bagaimana energi ditransfer dalam sistem. Teruslah berlatih, kamu sudah di jalur yang benar!`;
    }
  }
}

/**
 * Process multiple questions and explanations in batches to avoid rate limiting
 * Includes mechanisms to avoid duplicate or similar questions across batches
 *
 * @param count Number of questions to generate
 * @param level Difficulty level
 * @param topic Topic for the questions
 * @param subject Subject area
 * @param customExplanationPrompt Optional custom prompt for explanations only (not used for question generation)
 * @param batchSize Number of questions to generate in each batch
 * @param delayBetweenBatches Delay in ms between batches to avoid rate limiting
 * @returns Object containing generated questions and explanations
 *
 * Improvements to reduce question similarity:
 * 1. Tracks previously generated questions to avoid duplicates
 * 2. Uses different prompt variations for each batch
 * 3. Increases temperature parameter for later batches to encourage diversity
 * 4. Implements similarity detection to reject questions too similar to existing ones
 */
export async function processQuestionsAndExplanations(
  count: number,
  level: 'easy' | 'medium' | 'hard',
  topic: string = 'energy transformation',
  subject: string = 'science',
  customExplanationPrompt?: string,
  batchSize: number = 2, // Reduced from 3 to 2 to avoid overwhelming the API
  delayBetweenBatches: number = 5000, // Increased from 2000 to 5000ms
  onProgress?: (progress: number, currentCount: number, totalCount: number, status?: string) => void
): Promise<BatchProcessingResult> {
  const questions: AIGeneratedQuestion[] = [];
  const explanations: Record<string, string> = {};

  // Calculate number of batches needed
  const batches = Math.ceil(count / batchSize);

  console.log(`Generating ${count} questions in ${batches} batches of ${batchSize}...`);

  // Initial progress update
  if (onProgress) {
    onProgress(0, 0, count, "Starting question generation");
  }

  // Function to update progress
  const updateProgress = (current: number, total: number, status?: string) => {
    const progressPercent = Math.floor((current / total) * 100);
    console.log(`Progress: ${progressPercent}% (${current}/${total} questions)${status ? ` - ${status}` : ''}`);

    // Call the progress callback if provided
    if (onProgress) {
      onProgress(progressPercent, current, total, status);
    }
  };

  // Set a maximum time for the entire process
  const startTime = Date.now();
  const MAX_PROCESS_TIME = 45000; // 45 seconds max for the entire process (increased from 30s)

  // Function to check if we've exceeded the max time
  const isTimeExceeded = () => {
    return (Date.now() - startTime) > MAX_PROCESS_TIME;
  };

  // Fetch existing questions from database for similarity checking
  console.log('Fetching existing questions from database for similarity checking...');
  const databaseQuestions = await getExistingQuestionsFromDatabase();

  // Filter database questions to only include those with the same topic and subject if possible
  const filteredDatabaseQuestions = databaseQuestions.filter(q => {
    // If topic and subject are defined in the database question, use them for filtering
    if (q.topic && q.subject) {
      return q.topic.toLowerCase().includes(topic.toLowerCase()) ||
             topic.toLowerCase().includes(q.topic.toLowerCase()) ||
             q.subject.toLowerCase() === subject.toLowerCase();
    }
    // Otherwise include all questions
    return true;
  });

  const databaseQuestionTexts = filteredDatabaseQuestions.map(q => q.text);
  console.log(`Found ${databaseQuestionTexts.length} existing questions in database for similarity checking (filtered from ${databaseQuestions.length} total questions)`);

  // Analyze existing questions to identify common patterns to avoid
  const commonWords = analyzeCommonPatterns(databaseQuestionTexts);
  console.log(`Identified common patterns to avoid: ${commonWords.join(', ')}`);

  // Immediately add at least one mock question to ensure we always have something to return
  const initialMockQuestion = getMockQuestionResponse(level);
  questions.push(initialMockQuestion);
  explanations[initialMockQuestion.text] = initialMockQuestion.explanation;
  console.log("Added initial mock question as fallback");

  try {
    for (let batch = 0; batch < batches; batch++) {
      // Check if we already have enough questions
      if (questions.length >= count) {
        console.log(`Already have ${questions.length}/${count} questions, stopping generation`);
        break;
      }

      // Check if we've exceeded the max time
      if (isTimeExceeded()) {
        console.log(`Maximum process time of ${MAX_PROCESS_TIME/1000} seconds exceeded, stopping generation`);
        break;
      }

      const remainingCount = count - questions.length;
      const currentBatchSize = Math.min(batchSize, remainingCount);
      const batchQuestions: AIGeneratedQuestion[] = [];

      console.log(`Batch ${batch + 1}/${batches}: Generating ${currentBatchSize} questions...`);

      // Ekstrak teks pertanyaan dari pertanyaan yang sudah dibuat untuk menghindari duplikasi
      const generatedQuestionTexts = questions.map(q => q.text);

      // Gabungkan dengan pertanyaan dari database untuk pemeriksaan kesamaan yang lebih komprehensif
      const existingQuestionTexts = [...generatedQuestionTexts, ...databaseQuestionTexts];
      console.log(`Tracking ${generatedQuestionTexts.length} generated questions and ${databaseQuestionTexts.length} database questions to avoid duplication (total: ${existingQuestionTexts.length})`);

      // Fungsi untuk menghitung similarity score antara dua string (0-1)
      const calculateSimilarity = (str1: string, str2: string): number => {
        // Jika salah satu string kosong, kembalikan 0
        if (!str1 || !str2) return 0;

        // Jika kedua string sama persis, kembalikan 1
        if (str1 === str2) return 1;

        // Ubah ke lowercase dan hapus tanda baca
        const s1 = str1.toLowerCase().replace(/[^\w\s]/g, '');
        const s2 = str2.toLowerCase().replace(/[^\w\s]/g, '');

        // Jika salah satu string kosong setelah pembersihan, kembalikan 0
        if (!s1 || !s2) return 0;

        // Jika kedua string sama setelah pembersihan, kembalikan 0.9 (hampir sama)
        if (s1 === s2) return 0.9;

        // Jika salah satu string merupakan substring dari yang lain, berikan skor tinggi
        if (s1.includes(s2)) return 0.8 * (s2.length / s1.length);
        if (s2.includes(s1)) return 0.8 * (s1.length / s2.length);

        // Hitung Jaccard similarity berdasarkan kata-kata
        const words1 = s1.split(/\s+/).filter(Boolean);
        const words2 = s2.split(/\s+/).filter(Boolean);

        // Jika salah satu tidak memiliki kata, kembalikan 0
        if (words1.length === 0 || words2.length === 0) return 0;

        // Hitung intersection (kata-kata yang sama)
        const intersection = words1.filter(word => words2.includes(word));

        // Hitung union (semua kata unik)
        const union = [...new Set([...words1, ...words2])];

        // Jaccard similarity: intersection / union
        const jaccardSimilarity = intersection.length / union.length;

        // Hitung cosine similarity berdasarkan kata-kata
        // Buat vektor frekuensi kata
        const allWords = [...new Set([...words1, ...words2])];
        const vector1 = allWords.map(word => words1.filter(w => w === word).length);
        const vector2 = allWords.map(word => words2.filter(w => w === word).length);

        // Hitung dot product
        let dotProduct = 0;
        for (let i = 0; i < allWords.length; i++) {
          dotProduct += vector1[i] * vector2[i];
        }

        // Hitung magnitude
        const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
        const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

        // Cosine similarity
        const cosineSimilarity = magnitude1 * magnitude2 !== 0 ?
          dotProduct / (magnitude1 * magnitude2) : 0;

        // Gabungkan kedua metrik dengan bobot
        return (jaccardSimilarity * 0.5) + (cosineSimilarity * 0.5);
      };

      // Fungsi untuk memeriksa kesamaan teks dengan threshold
      const hasSimilarQuestion = (text: string, threshold: number = 0.7): boolean => {
        // Jika teks persis sama, kembalikan true
        if (existingQuestionTexts.includes(text)) return true;

        // Periksa similarity dengan semua pertanyaan yang ada
        for (const existingQuestion of existingQuestionTexts) {
          const similarityScore = calculateSimilarity(text, existingQuestion);

          // Log similarity score untuk debugging
          if (similarityScore > 0.5) {
            console.log(`Similarity score: ${similarityScore.toFixed(2)} between:
            - New: ${text.substring(0, 50)}...
            - Existing: ${existingQuestion.substring(0, 50)}...`);
          }

          // Jika similarity score melebihi threshold, kembalikan true
          if (similarityScore >= threshold) {
            console.log(`High similarity detected (${similarityScore.toFixed(2)}):
            - New: ${text}
            - Existing: ${existingQuestion}`);
            return true;
          }
        }

        return false;
      };

      // Generate questions sequentially instead of in parallel to avoid rate limits
      for (let i = 0; i < currentBatchSize; i++) {
        // Check if we've exceeded the max time
        if (isTimeExceeded()) {
          console.log(`Maximum process time exceeded during question generation, stopping batch`);
          break;
        }

        try {
          console.log(`Generating question ${i + 1}/${currentBatchSize} in batch ${batch + 1}`);

          // Add a small delay between each question to avoid rate limiting
          if (i > 0) {
            const questionDelay = 1000 + Math.floor(Math.random() * 500); // 1-1.5 seconds (reduced)
            console.log(`Waiting ${questionDelay}ms before generating next question...`);
            await new Promise(resolve => setTimeout(resolve, questionDelay));
          }

          // Kirim daftar pertanyaan yang sudah ada ke generator untuk menghindari duplikasi
          // Batasi jumlah pertanyaan yang dikirim untuk menghindari prompt yang terlalu panjang
          const maxPreviousQuestions = 5; // Hanya kirim 5 pertanyaan terakhir
          const recentQuestions = existingQuestionTexts.slice(-maxPreviousQuestions);

          // Gunakan common words yang sudah diidentifikasi untuk meningkatkan keragaman
          // Set a timeout for question generation
          const questionPromise = generateQuestionWithGemini(level, topic, subject, batch, recentQuestions);
          const timeoutPromise = new Promise<AIGeneratedQuestion | null>((_, reject) => {
            setTimeout(() => {
              reject(new Error(`Question generation timed out`));
            }, 15000); // 15 second timeout per question (increased from 10s)
          });

          // Race the question generation against the timeout
          const question = await Promise.race([questionPromise, timeoutPromise])
            .catch(error => {
              console.error(`Timeout or error generating question: ${error.message}`);
              // Return mock question instead of null on timeout or error
              console.log("Using mock question due to timeout or error");
              return getMockQuestionResponse(level);
            });

          if (question) {
            // Periksa apakah pertanyaan ini mirip dengan pertanyaan yang sudah ada
            // Gunakan threshold yang berbeda untuk pertanyaan yang dihasilkan vs database
            // Lebih ketat untuk pertanyaan yang baru dihasilkan (0.7) vs database (0.65)
            const isDuplicateGenerated = hasSimilarQuestion(question.text, 0.7);

            // Jika pertanyaan mirip dengan yang sudah dihasilkan sebelumnya
            if (isDuplicateGenerated) {
              console.log(`Question "${question.text.substring(0, 50)}..." is too similar to a previously generated question. Skipping.`);

              // Jika ini adalah percobaan terakhir dan kita masih belum mendapatkan pertanyaan yang unik,
              // gunakan pertanyaan ini tapi dengan modifikasi kecil
              if (i === currentBatchSize - 1) {
                console.log("This is the last attempt, using the question with a small modification");
                // Tambahkan penanda untuk membedakan pertanyaan
                question.text = `[Variasi] ${question.text}`;
                batchQuestions.push(question);
                console.log(`Added modified question ${i + 1}`);
              }
              // Jika bukan percobaan terakhir, lanjutkan ke iterasi berikutnya
              continue;
            }

            // Periksa kesamaan dengan pertanyaan di database
            // Buat array yang hanya berisi teks pertanyaan dari database
            const dbOnlyQuestionTexts = databaseQuestionTexts;

            // Fungsi untuk memeriksa kesamaan dengan pertanyaan database saja
            const hasSimilarDbQuestion = (text: string, threshold: number = 0.65): boolean => {
              // Periksa similarity dengan pertanyaan database
              for (const existingQuestion of dbOnlyQuestionTexts) {
                const similarityScore = calculateSimilarity(text, existingQuestion);

                // Jika similarity score melebihi threshold, kembalikan true
                if (similarityScore >= threshold) {
                  console.log(`High similarity with database question (${similarityScore.toFixed(2)}):
                  - New: ${text.substring(0, 50)}...
                  - DB: ${existingQuestion.substring(0, 50)}...`);
                  return true;
                }
              }
              return false;
            };

            // Periksa kesamaan dengan pertanyaan di database
            const isDuplicateDb = hasSimilarDbQuestion(question.text);

            if (isDuplicateDb) {
              console.log(`Question "${question.text.substring(0, 50)}..." is too similar to an existing database question. Skipping.`);

              // Jika ini adalah percobaan terakhir dan kita masih belum mendapatkan pertanyaan yang unik,
              // gunakan pertanyaan ini tapi dengan modifikasi kecil
              if (i === currentBatchSize - 1) {
                console.log("This is the last attempt, using the question with a small modification");
                // Tambahkan penanda untuk membedakan pertanyaan
                question.text = `[Variasi DB] ${question.text}`;
                batchQuestions.push(question);
                console.log(`Added modified question ${i + 1} (similar to database question)`);
              }
              // Jika bukan percobaan terakhir, lanjutkan ke iterasi berikutnya
              continue;
            }

            // Jika pertanyaan unik, tambahkan ke batch
            batchQuestions.push(question);
            console.log(`Successfully generated unique question ${i + 1}`);

            // Update progress after each successful question
            updateProgress(questions.length + batchQuestions.length, count, "Generating questions");

            // Add the explanation immediately for this question
            try {
              console.log(`Generating explanation for question ${i + 1}`);

              // Set a timeout for explanation generation
              const explanationPromise = generateExplanationWithGemini(
                question.text,
                question.correct_answer,
                question.correct_answer,
                true, // isCorrect
                level,
                customExplanationPrompt
              );

              // Create a timeout promise
              const explanationTimeoutPromise = new Promise<string | null>((_, reject) => {
                setTimeout(() => {
                  reject(new Error(`Explanation generation timed out`));
                }, 12000); // 12 second timeout per explanation (increased from 10s)
              });

              // Race the explanation generation against the timeout
              const explanation = await Promise.race([explanationPromise, explanationTimeoutPromise])
                .catch(error => {
                  console.error(`Timeout or error generating explanation: ${error.message}`);
                  // Use default explanation from the question
                  console.log("Using default explanation due to timeout or error");
                  return question.explanation;
                });

              // Store the explanation (use fallback if needed)
              explanations[question.text] = explanation || question.explanation;
              console.log(`Explanation for question ${i + 1} ${explanation ? 'generated successfully' : 'used fallback'}`);
            } catch (explanationError) {
              console.error(`Error generating explanation for question ${i + 1}:`, explanationError);
              // Use the default explanation as fallback
              explanations[question.text] = question.explanation;
            }
          } else {
            console.warn(`Failed to generate question ${i + 1}, got null response`);
          }
        } catch (error) {
          console.error(`Error generating question ${i + 1}:`, error);
          // Continue with the next question
        }
      }

      console.log(`Batch ${batch + 1}: Generated ${batchQuestions.length}/${currentBatchSize} valid questions`);

      // If we didn't get any questions in this batch, try to use mock questions
      if (batchQuestions.length === 0) {
        console.warn(`Batch ${batch + 1} failed to generate any questions, using mock questions`);

        // Add mock questions for this batch
        for (let i = 0; i < currentBatchSize; i++) {
          const mockQuestion = getMockQuestionResponse(level);
          batchQuestions.push(mockQuestion);
          // Add the explanation for the mock question
          explanations[mockQuestion.text] = mockQuestion.explanation;
        }
      }

      // Add batch questions to the main questions array
      questions.push(...batchQuestions);

      console.log(`Batch ${batch + 1} complete. Total questions: ${questions.length}/${count}`);
      updateProgress(questions.length, count, `Completed batch ${batch + 1}/${batches}`);

      // Add delay between batches to avoid rate limiting
      if (batch < batches - 1 && questions.length < count && !isTimeExceeded()) {
        const batchDelay = delayBetweenBatches + Math.floor(Math.random() * 1000); // Add some randomness
        console.log(`Waiting ${batchDelay}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    }
  } catch (error) {
    console.error("Error in batch processing:", error);

    // If we have at least some questions, return them
    if (questions.length > 0) {
      console.log(`Returning ${questions.length} questions despite errors`);
    } else {
      // If we have no questions at all, add mock questions
      console.log("No questions generated, adding mock questions");
      for (let i = 0; i < count; i++) {
        const mockQuestion = getMockQuestionResponse(level);
        questions.push(mockQuestion);
        explanations[mockQuestion.text] = mockQuestion.explanation;
      }
    }
  }

  // If we don't have enough questions, fill with mock questions
  if (questions.length < count) {
    console.log(`Only generated ${questions.length}/${count} questions, adding ${count - questions.length} mock questions`);
    for (let i = 0; i < (count - questions.length); i++) {
      const mockQuestion = getMockQuestionResponse(level);
      questions.push(mockQuestion);
      explanations[mockQuestion.text] = mockQuestion.explanation;
    }
  }

  // Pastikan kita selalu mengembalikan setidaknya satu pertanyaan
  if (questions.length === 0) {
    console.warn("Tidak ada pertanyaan yang berhasil dibuat, mengembalikan pertanyaan contoh");
    const mockQuestion = getMockQuestionResponse(level);
    questions.push(mockQuestion);
    explanations[mockQuestion.text] = mockQuestion.explanation;
  }

  // Log hasil akhir
  console.log(`Mengembalikan ${Math.min(questions.length, count)} pertanyaan dari ${questions.length} yang dibuat`);

  // Final progress update
  if (onProgress) {
    onProgress(100, count, count, "Question generation complete");
  }

  // Return the generated questions and explanations
  return {
    questions: questions.slice(0, count),
    explanations
  };
}
