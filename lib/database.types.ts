export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      questions: {
        Row: {
          id: string
          text: string
          options: string[]
          correct_answer: string
          difficulty: 'easy' | 'medium' | 'hard'
          explanation: string
          created_at: string
          updated_at: string | null
          question_type: 'pre-test' | 'main-test'
          subject: string
          topic: string
          grade_level: number
        }
        Insert: {
          id?: string
          text: string
          options: string[]
          correct_answer: string
          difficulty: 'easy' | 'medium' | 'hard'
          explanation: string
          created_at?: string
          updated_at?: string | null
          question_type: 'pre-test' | 'main-test'
          subject?: string
          topic?: string
          grade_level?: number
        }
        Update: {
          id?: string
          text?: string
          options?: string[]
          correct_answer?: string
          difficulty?: 'easy' | 'medium' | 'hard'
          explanation?: string
          created_at?: string
          updated_at?: string | null
          question_type?: 'pre-test' | 'main-test'
          subject?: string
          topic?: string
          grade_level?: number
        }
      }
      students: {
        Row: {
          id: string
          name: string
          student_id: string
          current_level: 'easy' | 'medium' | 'hard'
          created_at: string
          updated_at: string | null
          grade: number
          class_section: string | null
          last_login: string | null
        }
        Insert: {
          id?: string
          name: string
          student_id: string
          current_level?: 'easy' | 'medium' | 'hard'
          created_at?: string
          updated_at?: string | null
          grade?: number
          class_section?: string | null
          last_login?: string | null
        }
        Update: {
          id?: string
          name?: string
          student_id?: string
          current_level?: 'easy' | 'medium' | 'hard'
          created_at?: string
          updated_at?: string | null
          grade?: number
          class_section?: string | null
          last_login?: string | null
        }
      }
      teachers: {
        Row: {
          id: string
          auth_id: string
          name: string | null
          email: string
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          auth_id: string
          name?: string | null
          email: string
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          auth_id?: string
          name?: string | null
          email?: string
          created_at?: string
          updated_at?: string | null
        }
      }
      classes: {
        Row: {
          id: string
          name: string
          teacher_id: string
          grade_level: number
          section: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          teacher_id: string
          grade_level: number
          section?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          teacher_id?: string
          grade_level?: number
          section?: string | null
          created_at?: string
          updated_at?: string | null
        }
      }
      student_classes: {
        Row: {
          id: string
          student_id: string
          class_id: string
          created_at: string
        }
        Insert: {
          id?: string
          student_id: string
          class_id: string
          created_at?: string
        }
        Update: {
          id?: string
          student_id?: string
          class_id?: string
          created_at?: string
        }
      }
      test_results: {
        Row: {
          id: string
          student_id: string
          test_type: 'pre-test' | 'main-test'
          score: number
          total_questions: number
          passed: boolean
          previous_level: 'easy' | 'medium' | 'hard' | null
          new_level: 'easy' | 'medium' | 'hard'
          created_at: string
          completed_at: string
          time_spent: number | null
          subject: string
          topic: string
        }
        Insert: {
          id?: string
          student_id: string
          test_type: 'pre-test' | 'main-test'
          score: number
          total_questions: number
          passed: boolean
          previous_level?: 'easy' | 'medium' | 'hard' | null
          new_level: 'easy' | 'medium' | 'hard'
          created_at?: string
          completed_at?: string
          time_spent?: number | null
          subject?: string
          topic?: string
        }
        Update: {
          id?: string
          student_id?: string
          test_type?: 'pre-test' | 'main-test'
          score?: number
          total_questions?: number
          passed?: boolean
          previous_level?: 'easy' | 'medium' | 'hard' | null
          new_level?: 'easy' | 'medium' | 'hard'
          created_at?: string
          completed_at?: string
          time_spent?: number | null
          subject?: string
          topic?: string
        }
      }
      student_answers: {
        Row: {
          id: string
          test_result_id: string
          question_id: string | null
          student_answer: string
          is_correct: boolean
          time_spent: number | null
          created_at: string
        }
        Insert: {
          id?: string
          test_result_id: string
          question_id?: string | null
          student_answer: string
          is_correct: boolean
          time_spent?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          test_result_id?: string
          question_id?: string | null
          student_answer?: string
          is_correct?: boolean
          time_spent?: number | null
          created_at?: string
        }
      }
      settings: {
        Row: {
          id: string
          kkm: number
          pre_test_questions: number
          main_test_questions: number
          enable_ai: boolean
          ai_model: string
          ai_prompt: string
          created_at: string
          updated_at: string | null
          school_name: string
          school_logo_url: string | null
          theme_color: string
          enable_student_registration: boolean
        }
        Insert: {
          id?: string
          kkm: number
          pre_test_questions: number
          main_test_questions: number
          enable_ai: boolean
          ai_model: string
          ai_prompt: string
          created_at?: string
          updated_at?: string | null
          school_name?: string
          school_logo_url?: string | null
          theme_color?: string
          enable_student_registration?: boolean
        }
        Update: {
          id?: string
          kkm?: number
          pre_test_questions?: number
          main_test_questions?: number
          enable_ai?: boolean
          ai_model?: string
          ai_prompt?: string
          created_at?: string
          updated_at?: string | null
          school_name?: string
          school_logo_url?: string | null
          theme_color?: string
          enable_student_registration?: boolean
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      difficulty_level: 'easy' | 'medium' | 'hard'
      question_type_enum: 'pre-test' | 'main-test'
      test_type_enum: 'pre-test' | 'main-test'
    }
  }
}
