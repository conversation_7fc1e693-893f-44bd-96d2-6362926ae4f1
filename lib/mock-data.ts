export interface Question {
  id: string
  text: string
  options: string[]
  correctAnswer: string
  difficulty: "easy" | "medium" | "hard"
  explanation: string
}

export const preTestQuestions: Question[] = [
  {
    id: "pre-1",
    text: "Apa yang terjadi pada energi kimia dalam baterai ketika Anda menyalakan senter?",
    options: [
      "Energi menghilang",
      "Energi berubah menjadi energi cahaya dan panas",
      "Energi tetap sama",
      "Energi berubah menjadi energi suara",
    ],
    correctAnswer: "Energi berubah menjadi energi cahaya dan panas",
    difficulty: "easy",
    explanation:
      "<PERSON><PERSON><PERSON> Anda menyalakan senter, energi kimia yang tersimpan dalam baterai berubah menjadi energi listrik, yang kemudian berubah menjadi energi cahaya dan sebagian energi panas.",
  },
  {
    id: "pre-2",
    text: "Transformasi energi apa yang terjadi ketika Anda menggosok-gosokkan tangan Anda?",
    options: ["<PERSON><PERSON> menjadi panas", "Mekanik menjadi panas", "<PERSON>rik menjadi panas", "Cahaya menjadi panas"],
    correctAnswer: "Mekanik menjadi panas",
    difficulty: "easy",
    explanation:
      "Ketika Anda menggosok-gosokkan tangan, energi mekanik (gerakan) berubah menjadi energi panas karena gesekan antara kedua tangan Anda.",
  },
  {
    id: "pre-3",
    text: "Transformasi energi apa yang terjadi pada panel surya?",
    options: ["Panas menjadi listrik", "Cahaya menjadi listrik", "Kimia menjadi listrik", "Mekanik menjadi listrik"],
    correctAnswer: "Cahaya menjadi listrik",
    difficulty: "medium",
    explanation:
      "Panel surya mengandung sel fotovoltaik yang mengubah energi cahaya dari matahari menjadi energi listrik yang dapat menggerakkan perangkat atau disimpan dalam baterai.",
  },
  {
    id: "pre-4",
    text: "Pada turbin angin, apa transformasi energi utama yang terjadi?",
    options: [
      "Angin menjadi mekanik menjadi listrik",
      "Angin menjadi panas menjadi listrik",
      "Angin menjadi suara menjadi listrik",
      "Angin menjadi cahaya menjadi listrik",
    ],
    correctAnswer: "Angin menjadi mekanik menjadi listrik",
    difficulty: "medium",
    explanation:
      "Turbin angin menangkap energi kinetik angin, yang memutar bilah (energi mekanik), dan kemudian generator mengubah energi mekanik ini menjadi energi listrik.",
  },
  {
    id: "pre-5",
    text: "Transformasi energi apa yang terjadi ketika Anda berbicara ke mikrofon?",
    options: ["Suara menjadi listrik", "Mekanik menjadi suara", "Kimia menjadi suara", "Listrik menjadi suara"],
    correctAnswer: "Suara menjadi listrik",
    difficulty: "medium",
    explanation:
      "Ketika Anda berbicara ke mikrofon, gelombang suara (energi suara) menyebabkan diafragma bergetar, yang kemudian diubah menjadi energi listrik yang dapat diperkuat atau direkam.",
  },
  {
    id: "pre-6",
    text: "Transformasi energi apa yang terjadi pada pemanggang roti?",
    options: ["Listrik menjadi panas", "Kimia menjadi panas", "Mekanik menjadi panas", "Nuklir menjadi panas"],
    correctAnswer: "Listrik menjadi panas",
    difficulty: "easy",
    explanation:
      "Pemanggang roti mengubah energi listrik menjadi energi panas. Arus listrik mengalir melalui elemen resistif yang memanas dan memanggang roti.",
  },
  {
    id: "pre-7",
    text: "Transformasi energi apa yang terjadi pada mesin mobil?",
    options: ["Listrik menjadi mekanik", "Kimia menjadi mekanik", "Panas menjadi mekanik", "Cahaya menjadi mekanik"],
    correctAnswer: "Kimia menjadi mekanik",
    difficulty: "medium",
    explanation:
      "Dalam mesin mobil, energi kimia yang tersimpan dalam bensin dilepaskan melalui pembakaran dan diubah menjadi energi mekanik yang menggerakkan piston dan akhirnya roda.",
  },
  {
    id: "pre-8",
    text: "Ketika bola menggelinding turun bukit dan perlahan berhenti, apa yang terjadi pada energinya?",
    options: [
      "Energi menghilang sepenuhnya",
      "Energi berubah menjadi energi potensial",
      "Energi berubah menjadi energi panas dan suara",
      "Energi tetap sebagai energi kinetik",
    ],
    correctAnswer: "Energi berubah menjadi energi panas dan suara",
    difficulty: "hard",
    explanation:
      "Saat bola menggelinding turun bukit, energi potensialnya berubah menjadi energi kinetik. Ketika perlahan berhenti, energi kinetik berubah menjadi energi panas dan suara karena gesekan dengan tanah.",
  },
  {
    id: "pre-9",
    text: "Transformasi energi apa yang terjadi pada bendungan pembangkit listrik tenaga air?",
    options: ["Listrik menjadi mekanik", "Potensial menjadi listrik", "Kimia menjadi listrik", "Nuklir menjadi listrik"],
    correctAnswer: "Potensial menjadi listrik",
    difficulty: "hard",
    explanation:
      "Pada bendungan pembangkit listrik tenaga air, energi potensial air yang tersimpan di ketinggian berubah menjadi energi kinetik saat jatuh, yang kemudian memutar turbin (energi mekanik) yang menghasilkan energi listrik.",
  },
  {
    id: "pre-10",
    text: "Transformasi energi apa yang terjadi ketika Anda menggunakan senter engkol tangan?",
    options: [
      "Mekanik menjadi listrik menjadi cahaya",
      "Kimia menjadi listrik menjadi cahaya",
      "Panas menjadi listrik menjadi cahaya",
      "Suara menjadi listrik menjadi cahaya",
    ],
    correctAnswer: "Mekanik menjadi listrik menjadi cahaya",
    difficulty: "medium",
    explanation:
      "Ketika Anda memutar engkol senter engkol tangan, energi mekanik Anda diubah menjadi energi listrik oleh generator, yang kemudian diubah menjadi energi cahaya oleh bola lampu.",
  },
  {
    id: "pre-11",
    text: "Transformasi energi apa yang terjadi pada tumbuhan selama fotosintesis?",
    options: ["Cahaya menjadi kimia", "Panas menjadi kimia", "Listrik menjadi kimia", "Mekanik menjadi kimia"],
    correctAnswer: "Cahaya menjadi kimia",
    difficulty: "hard",
    explanation:
      "Selama fotosintesis, tumbuhan menangkap energi cahaya dari matahari dan mengubahnya menjadi energi kimia yang disimpan dalam molekul glukosa (gula).",
  },
  {
    id: "pre-12",
    text: "Transformasi energi apa yang terjadi ketika Anda menyalakan korek api?",
    options: [
      "Mekanik menjadi kimia menjadi panas dan cahaya",
      "Kimia menjadi panas dan cahaya",
      "Listrik menjadi panas dan cahaya",
      "Nuklir menjadi panas dan cahaya",
    ],
    correctAnswer: "Mekanik menjadi kimia menjadi panas dan cahaya",
    difficulty: "hard",
    explanation:
      "Ketika Anda menyalakan korek api, energi mekanik dari gesekan memicu reaksi kimia di kepala korek api, yang melepaskan energi dalam bentuk panas dan cahaya.",
  },
  {
    id: "pre-13",
    text: "Transformasi energi apa yang terjadi pada pengeras suara?",
    options: ["Listrik menjadi suara", "Mekanik menjadi suara", "Kimia menjadi suara", "Panas menjadi suara"],
    correctAnswer: "Listrik menjadi suara",
    difficulty: "easy",
    explanation:
      "Pada pengeras suara, energi listrik diubah menjadi energi mekanik (gerakan kerucut speaker), yang menciptakan gelombang suara (energi suara) yang dapat kita dengar.",
  },
  {
    id: "pre-14",
    text: "Transformasi energi apa yang terjadi pada tongkat cahaya (glow stick)?",
    options: ["Listrik menjadi cahaya", "Kimia menjadi cahaya", "Nuklir menjadi cahaya", "Mekanik menjadi cahaya"],
    correctAnswer: "Kimia menjadi cahaya",
    difficulty: "easy",
    explanation:
      "Ketika Anda menekuk tongkat cahaya, Anda memecahkan tabung kaca kecil di dalamnya, memungkinkan dua bahan kimia bercampur. Reaksi kimia ini melepaskan energi dalam bentuk cahaya, proses yang disebut kemiluminesensi.",
  },
  {
    id: "pre-15",
    text: "Transformasi energi apa yang terjadi ketika makanan dicerna dalam tubuh Anda?",
    options: [
      "Mekanik menjadi kimia",
      "Kimia menjadi mekanik dan panas",
      "Cahaya menjadi kimia",
      "Listrik menjadi kimia",
    ],
    correctAnswer: "Kimia menjadi mekanik dan panas",
    difficulty: "hard",
    explanation:
      "Ketika tubuh Anda mencerna makanan, energi kimia yang tersimpan dalam molekul makanan dilepaskan dan diubah menjadi energi mekanik untuk pergerakan, energi listrik untuk sinyal saraf, dan energi panas untuk mempertahankan suhu tubuh.",
  },
]

export const mainTestQuestions: {
  easy: Question[]
  medium: Question[]
  hard: Question[]
} = {
  easy: [
    {
      id: "easy-1",
      text: "What energy transformation occurs in a light bulb?",
      options: ["Chemical to light", "Electrical to light and heat", "Nuclear to light", "Mechanical to light"],
      correctAnswer: "Electrical to light and heat",
      difficulty: "easy",
      explanation:
        "A light bulb transforms electrical energy into light energy and heat energy. Most traditional incandescent bulbs actually convert more energy to heat than to light.",
    },
    {
      id: "easy-2",
      text: "Which energy transformation happens when you use a battery-powered toy?",
      options: [
        "Chemical to electrical to mechanical",
        "Electrical to chemical to mechanical",
        "Mechanical to electrical to chemical",
        "Heat to electrical to mechanical",
      ],
      correctAnswer: "Chemical to electrical to mechanical",
      difficulty: "easy",
      explanation:
        "In a battery-powered toy, chemical energy stored in the battery is transformed into electrical energy, which powers motors that create mechanical energy (movement).",
    },
    // Add more easy questions here
  ],
  medium: [
    {
      id: "medium-1",
      text: "What energy transformation occurs in a microphone?",
      options: ["Electrical to sound", "Sound to electrical", "Mechanical to sound", "Chemical to sound"],
      correctAnswer: "Sound to electrical",
      difficulty: "medium",
      explanation:
        "A microphone transforms sound energy (sound waves) into electrical energy. The sound waves cause a diaphragm to vibrate, which creates corresponding electrical signals.",
    },
    {
      id: "medium-2",
      text: "Which energy transformation happens during cellular respiration?",
      options: [
        "Light to chemical",
        "Chemical to heat and ATP (chemical)",
        "Electrical to chemical",
        "Mechanical to chemical",
      ],
      correctAnswer: "Chemical to heat and ATP (chemical)",
      difficulty: "medium",
      explanation:
        "During cellular respiration, the chemical energy stored in glucose is transformed into ATP (another form of chemical energy) and heat energy. ATP is the energy currency that cells use to power various processes.",
    },
    // Add more medium questions here
  ],
  hard: [
    {
      id: "hard-1",
      text: "What energy transformations occur in a nuclear power plant?",
      options: [
        "Nuclear to heat to mechanical to electrical",
        "Chemical to heat to mechanical to electrical",
        "Potential to kinetic to electrical",
        "Electrical to nuclear to heat",
      ],
      correctAnswer: "Nuclear to heat to mechanical to electrical",
      difficulty: "hard",
      explanation:
        "In a nuclear power plant, nuclear energy from uranium is transformed into heat energy through nuclear fission. This heat converts water to steam, which drives turbines (mechanical energy), which then generate electrical energy.",
    },
    {
      id: "hard-2",
      text: "Which energy transformation best describes what happens in a piezoelectric crystal when it's compressed?",
      options: ["Chemical to electrical", "Mechanical to electrical", "Heat to electrical", "Light to electrical"],
      correctAnswer: "Mechanical to electrical",
      difficulty: "hard",
      explanation:
        "When a piezoelectric crystal is compressed (mechanical energy), it generates a small electrical voltage (electrical energy). This property is used in many devices like electric lighters and some microphones.",
    },
    // Add more hard questions here
  ],
}

export const getRandomQuestions = (difficulty: "easy" | "medium" | "hard", count: number): Question[] => {
  const questions = mainTestQuestions[difficulty]
  const shuffled = [...questions].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

export const determineLevel = (score: number, totalQuestions: number): "easy" | "medium" | "hard" => {
  const percentage = (score / totalQuestions) * 100

  if (percentage < 40) return "easy"
  if (percentage < 75) return "medium"
  return "hard"
}
