import { supabase, supabaseAdmin } from './supabase';
import { Database } from './database.types';

// Types
export type Question = Database['public']['Tables']['questions']['Row'];
export type Student = Database['public']['Tables']['students']['Row'];
export type TestResult = Database['public']['Tables']['test_results']['Row'];
export type Settings = Database['public']['Tables']['settings']['Row'];

// Questions API
export async function getAllQuestions() {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('questions')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all questions:', error);
    return [];
  }

  return data;
}

export async function getPreTestQuestions() {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('question_type', 'pre-test');

  if (error) {
    console.error('Error fetching pre-test questions:', error);
    return [];
  }

  return data;
}

export async function getMainTestQuestions(difficulty: 'easy' | 'medium' | 'hard', count: number, useAI: boolean = false) {
  // If AI is not enabled, use the database questions
  if (!useAI) {
    const { data, error } = await supabase
      .from('questions')
      .select('*')
      .eq('question_type', 'main-test')
      .eq('difficulty', difficulty)
      .limit(count);

    if (error) {
      console.error(`Error fetching ${difficulty} main test questions:`, error);
      return [];
    }

    return data;
  } else {
    // If AI is enabled, we'll generate questions with Gemini AI
    // This will be implemented in the main-test page
    // For now, return database questions as a fallback
    const { data, error } = await supabase
      .from('questions')
      .select('*')
      .eq('question_type', 'main-test')
      .eq('difficulty', difficulty)
      .limit(count);

    if (error) {
      console.error(`Error fetching ${difficulty} main test questions:`, error);
      return [];
    }

    return data;
  }
}

export async function createQuestion(question: Database['public']['Tables']['questions']['Insert']) {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('questions')
    .insert(question)
    .select();

  if (error) {
    console.error('Error creating question:', error);
    return null;
  }

  return data[0];
}

export async function updateQuestion(id: string, updates: Database['public']['Tables']['questions']['Update']) {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('questions')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating question:', error);
    return null;
  }

  return data[0];
}

export async function deleteQuestion(id: string) {
  // Use supabaseAdmin to bypass RLS policies
  const { error } = await supabaseAdmin
    .from('questions')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting question:', error);
    return false;
  }

  return true;
}

// Students API
export async function getStudents() {
  const { data, error } = await supabase
    .from('students')
    .select('*');

  if (error) {
    console.error('Error fetching students:', error);
    return [];
  }

  return data;
}

export async function getStudentByStudentId(studentId: string) {
  // Use supabaseAdmin to bypass RLS policies and avoid using .single() to prevent errors
  const { data, error } = await supabaseAdmin
    .from('students')
    .select('*')
    .eq('student_id', studentId);

  if (error) {
    console.error('Error fetching student:', error);
    return null;
  }

  // Return the first matching student or null if none found
  return data && data.length > 0 ? data[0] : null;
}

export async function createStudent(student: Database['public']['Tables']['students']['Insert']) {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('students')
    .insert(student)
    .select();

  if (error) {
    console.error('Error creating student:', error);
    return null;
  }

  return data[0];
}

export async function updateStudent(id: string, updates: Database['public']['Tables']['students']['Update']) {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('students')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating student:', error);
    return null;
  }

  return data[0];
}

export async function updateStudentLevel(studentId: string, newLevel: 'easy' | 'medium' | 'hard') {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('students')
    .update({
      current_level: newLevel,
      updated_at: new Date().toISOString()
    })
    .eq('student_id', studentId)
    .select();

  if (error) {
    console.error('Error updating student level:', error);
    return null;
  }

  return data[0];
}

// Test Results API
export async function saveTestResult(result: Database['public']['Tables']['test_results']['Insert']) {
  // Use supabaseAdmin to bypass RLS policies
  const { data, error } = await supabaseAdmin
    .from('test_results')
    .insert(result)
    .select();

  if (error) {
    console.error('Error saving test result:', error);
    return null;
  }

  return data[0];
}

export async function getStudentTestResults(studentId: string) {
  const { data, error } = await supabase
    .from('test_results')
    .select('*')
    .eq('student_id', studentId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching student test results:', error);
    return [];
  }

  return data;
}

export async function getAllTestResults() {
  const { data, error } = await supabase
    .from('test_results')
    .select('*, students(name, student_id)')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all test results:', error);
    return [];
  }

  return data;
}

// Settings API
export async function getSettings() {
  try {
    const { data, error } = await supabaseAdmin
      .from('settings')
      .select('*')
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching settings:', error);
      // Return default settings as fallback
      return {
        kkm: 70, // Minimum passing score percentage
        pre_test_questions: 5,
        main_test_questions: 10,
        enable_ai: true,
        ai_model: "gemini-2.0-flash",
        ai_provider: "gemini",
        ai_prompt: "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
        school_name: "Science Academy",
        school_logo_url: null,
        theme_color: "#4F46E5",
        enable_student_registration: true
      };
    }

    // Ensure all required fields exist with defaults
    const settings = {
      kkm: 70,
      pre_test_questions: 5,
      main_test_questions: 10,
      enable_ai: true,
      ai_model: "gemini-1.5-flash",
      ai_prompt: "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
      school_name: "Science Academy",
      school_logo_url: null,
      theme_color: "#4F46E5",
      enable_student_registration: true,
      teacher_name: "Ms. Johnson",
      grade_level: "5",
      subject: "science",
      topic: "Energy Transformation",
      academic_year: "2023-2024",
      auto_grade: true,
      show_explanation: true,
      allow_retry: true,
      max_retries: 3,
      difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
      notify_teacher: true,
      notify_parent: false,
      theme: "default",
      email_template: "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]",
      ...data, // Override defaults with actual data
      // Add ai_provider for backward compatibility
      ai_provider: data.ai_model || "gemini-2.0-flash"
    };

    console.log("API getSettings returning:", settings);
    return settings;
  } catch (error) {
    console.error('Error in getSettings:', error);
    // Return default settings as fallback
    return {
      kkm: 70,
      pre_test_questions: 5,
      main_test_questions: 10,
      enable_ai: true,
      ai_model: "gemini-2.0-flash",
      ai_provider: "gemini",
      ai_prompt: "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
      school_name: "Science Academy",
      school_logo_url: null,
      theme_color: "#4F46E5",
      enable_student_registration: true,
      teacher_name: "Ms. Johnson",
      grade_level: "5",
      subject: "science",
      topic: "Energy Transformation",
      academic_year: "2023-2024",
      auto_grade: true,
      show_explanation: true,
      allow_retry: true,
      max_retries: 3,
      difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
      notify_teacher: true,
      notify_parent: false,
      theme: "default",
      email_template: "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]"
    };
  }
}

export async function updateSettings(settings: any) {
  try {
    // First get the existing settings to get the ID
    const { data: existingData, error: fetchError } = await supabaseAdmin
      .from('settings')
      .select('id')
      .limit(1)
      .single();

    if (fetchError) {
      console.error('Error fetching existing settings:', fetchError);
      return null;
    }

    // Prepare the update data
    const updateData = {
      // Include only the fields that exist in the database schema
      kkm: settings.kkm !== undefined ? settings.kkm : 70,
      pre_test_questions: settings.pre_test_questions !== undefined ? settings.pre_test_questions : 5,
      main_test_questions: settings.main_test_questions !== undefined ? settings.main_test_questions : 10,
      enable_ai: typeof settings.enable_ai === 'boolean' ? settings.enable_ai : true,
      ai_model: settings.ai_model !== undefined && settings.ai_model !== '' ? settings.ai_model : (settings.ai_provider || "gemini-2.0-flash"),
      ai_prompt: settings.ai_prompt !== undefined ? settings.ai_prompt : "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
      school_name: settings.school_name !== undefined && settings.school_name !== '' ? settings.school_name : "Science Academy",
      school_logo_url: settings.school_logo_url,
      theme_color: settings.theme_color !== undefined && settings.theme_color !== '' ? settings.theme_color : "#4F46E5",
      enable_student_registration: typeof settings.enable_student_registration === 'boolean' ? settings.enable_student_registration : true,
      teacher_name: settings.teacher_name !== undefined && settings.teacher_name !== '' ? settings.teacher_name : "Ms. Johnson",
      grade_level: settings.grade_level !== undefined && settings.grade_level !== '' ? settings.grade_level : "5",
      subject: settings.subject !== undefined && settings.subject !== '' ? settings.subject : "science",
      topic: settings.topic !== undefined && settings.topic !== '' ? settings.topic : "Energy Transformation",
      academic_year: settings.academic_year !== undefined && settings.academic_year !== '' ? settings.academic_year : "2023-2024",
      auto_grade: typeof settings.auto_grade === 'boolean' ? settings.auto_grade : true,
      show_explanation: typeof settings.show_explanation === 'boolean' ? settings.show_explanation : true,
      allow_retry: typeof settings.allow_retry === 'boolean' ? settings.allow_retry : true,
      max_retries: settings.max_retries !== undefined ? settings.max_retries : 3,
      difficulty_distribution: settings.difficulty_distribution !== undefined ? settings.difficulty_distribution : { easy: 30, medium: 40, hard: 30 },
      notify_teacher: typeof settings.notify_teacher === 'boolean' ? settings.notify_teacher : true,
      notify_parent: typeof settings.notify_parent === 'boolean' ? settings.notify_parent : false,
      theme: settings.theme !== undefined && settings.theme !== '' ? settings.theme : "default",
      email_template: settings.email_template !== undefined && settings.email_template !== '' ? settings.email_template : "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]",
      updated_at: new Date().toISOString()
    };

    // Log the update data for debugging
    console.log("Updating settings with:", updateData);
    console.log("AI Prompt being saved:", updateData.ai_prompt);

    console.log("Updating settings with ID:", existingData.id);

    let updatedData;

    try {
      // First, perform the update operation without selecting
      const { error: updateError } = await supabaseAdmin
        .from('settings')
        .update(updateData)
        .eq('id', existingData.id);

      if (updateError) {
        console.error('Error updating settings:', updateError);
        console.error('Error details:', updateError.details);
        console.error('Error hint:', updateError.hint);
        console.error('Error message:', updateError.message);
        return null;
      }

      console.log("Settings updated successfully, now fetching updated data");

      // Then, perform a separate select operation to get the updated data
      const { data: fetchedData, error: fetchError } = await supabaseAdmin
        .from('settings')
        .select('*')
        .eq('id', existingData.id)
        .single();

      if (fetchError) {
        console.error('Error fetching updated settings:', fetchError);
        return null;
      }

      if (!fetchedData) {
        console.error('No data returned after fetching updated settings');

        // Try one more time with a delay
        await new Promise(resolve => setTimeout(resolve, 500));

        const { data: retryData, error: retryError } = await supabaseAdmin
          .from('settings')
          .select('*')
          .eq('id', existingData.id)
          .single();

        if (retryError || !retryData) {
          console.error('Error fetching updated settings after retry:', retryError);
          return null;
        }

        console.log("Updated settings fetched successfully after retry:", retryData);
        updatedData = [retryData]; // Wrap in array to match previous format
      } else {
        console.log("Updated settings fetched successfully:", fetchedData);
        updatedData = [fetchedData]; // Wrap in array to match previous format
      }
    } catch (updateError) {
      console.error('Exception during settings update:', updateError);
      return null;
    }

    // Add ai_provider back for the return value and ensure all fields exist
    const returnData = {
      // Default values
      kkm: 70,
      pre_test_questions: 5,
      main_test_questions: 10,
      enable_ai: true,
      ai_model: "gemini-1.5-flash",
      ai_prompt: "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
      school_name: "Science Academy",
      school_logo_url: null,
      theme_color: "#4F46E5",
      enable_student_registration: true,
      teacher_name: "Ms. Johnson",
      grade_level: "5",
      subject: "science",
      topic: "Energy Transformation",
      academic_year: "2023-2024",
      auto_grade: true,
      show_explanation: true,
      allow_retry: true,
      max_retries: 3,
      difficulty_distribution: { easy: 30, medium: 40, hard: 30 },
      notify_teacher: true,
      notify_parent: false,
      theme: "default",
      email_template: "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]",
      // Override with actual data
      ...updatedData[0],
      // Add ai_provider for backward compatibility
      ai_provider: updatedData[0]?.ai_model || "gemini-2.0-flash"
    };

    // Ensure ai_prompt is included in the return data
    if (updateData.ai_prompt) {
      returnData.ai_prompt = updateData.ai_prompt;
    }

    console.log("API updateSettings returning:", returnData);
    console.log("AI Prompt in return data:", returnData.ai_prompt);
    return returnData;
  } catch (error) {
    console.error('Error in updateSettings:', error);
    return null;
  }
}

// Helper function to determine level based on score
export function determineLevel(score: number, totalQuestions: number): 'easy' | 'medium' | 'hard' {
  const percentage = (score / totalQuestions) * 100;

  if (percentage < 40) return 'easy';
  if (percentage < 75) return 'medium';
  return 'hard';
}
