// Direct database operations for when the Supabase client isn't working properly
import { supabase, supabaseAdmin } from './supabase';

export async function directUpdateSettings(settings: any) {
  try {
    console.log("Attempting direct settings update...");

    // First get the existing settings to get the ID
    const { data: existingData, error: fetchError } = await supabaseAdmin
      .from('settings')
      .select('id')
      .limit(1)
      .single();

    if (fetchError) {
      console.error('Error fetching existing settings:', fetchError);
      return null;
    }

    // Prepare the update data
    const updateData = {
      // Include only the fields that exist in the database schema
      kkm: settings.kkm !== undefined ? settings.kkm : 70,
      pre_test_questions: settings.pre_test_questions !== undefined ? settings.pre_test_questions : 5,
      main_test_questions: settings.main_test_questions !== undefined ? settings.main_test_questions : 10,
      enable_ai: typeof settings.enable_ai === 'boolean' ? settings.enable_ai : true,
      ai_model: settings.ai_model !== undefined && settings.ai_model !== '' ? settings.ai_model : "gemini",
      ai_prompt: settings.ai_prompt !== undefined ? settings.ai_prompt : "Explain why the answer to the following Grade 5 science question about energy transformation is [CORRECT_ANSWER]: [QUESTION_TEXT]",
      school_name: settings.school_name !== undefined && settings.school_name !== '' ? settings.school_name : "Science Academy",
      theme_color: settings.theme_color !== undefined && settings.theme_color !== '' ? settings.theme_color : "#4F46E5",
      enable_student_registration: typeof settings.enable_student_registration === 'boolean' ? settings.enable_student_registration : true,
      teacher_name: settings.teacher_name !== undefined && settings.teacher_name !== '' ? settings.teacher_name : "Ms. Johnson",
      grade_level: settings.grade_level !== undefined && settings.grade_level !== '' ? settings.grade_level : "5",
      subject: settings.subject !== undefined && settings.subject !== '' ? settings.subject : "science",
      topic: settings.topic !== undefined && settings.topic !== '' ? settings.topic : "Energy Transformation",
      academic_year: settings.academic_year !== undefined && settings.academic_year !== '' ? settings.academic_year : "2023-2024",
      auto_grade: typeof settings.auto_grade === 'boolean' ? settings.auto_grade : true,
      show_explanation: typeof settings.show_explanation === 'boolean' ? settings.show_explanation : true,
      allow_retry: typeof settings.allow_retry === 'boolean' ? settings.allow_retry : true,
      max_retries: settings.max_retries !== undefined ? settings.max_retries : 3,
      difficulty_distribution: settings.difficulty_distribution !== undefined ? settings.difficulty_distribution : { easy: 30, medium: 40, hard: 30 },
      notify_teacher: typeof settings.notify_teacher === 'boolean' ? settings.notify_teacher : true,
      notify_parent: typeof settings.notify_parent === 'boolean' ? settings.notify_parent : false,
      theme: settings.theme !== undefined && settings.theme !== '' ? settings.theme : "default",
      email_template: settings.email_template !== undefined && settings.email_template !== '' ? settings.email_template : "Dear [RECIPIENT_NAME],\n\n[STUDENT_NAME] has completed the [ASSESSMENT_NAME] assessment with a score of [SCORE]%.\n\n[PASS_FAIL_MESSAGE]\n\nYou can view the detailed results by logging into the assessment platform.\n\nBest regards,\n[TEACHER_NAME]\n[SCHOOL_NAME]",
      updated_at: new Date().toISOString()
    };

    // Log the update data for debugging
    console.log("Direct update with data:", updateData);

    // Try a different approach - update one field at a time
    let success = true;

    // Update each field individually
    for (const [key, value] of Object.entries(updateData)) {
      // Skip the updated_at field as it will be set automatically
      if (key === 'updated_at') continue;

      const updateObj = { [key]: value };
      console.log(`Updating field ${key} with value:`, value);

      const { error } = await supabaseAdmin
        .from('settings')
        .update(updateObj)
        .eq('id', existingData.id);

      if (error) {
        console.error(`Error updating field ${key}:`, error);
        success = false;
      }
    }

    if (!success) {
      console.error('Some fields failed to update');
    } else {
      console.log('All fields updated successfully');
    }

    // Get the updated settings
    const { data: updatedSettings, error: getError } = await supabaseAdmin
      .from('settings')
      .select('*')
      .eq('id', existingData.id)
      .single();

    if (getError) {
      console.error('Error fetching updated settings:', getError);
      return null;
    }

    if (!updatedSettings) {
      console.error('No data returned after fetching updated settings');

      // Try one more time with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const { data: retrySettings, error: retryError } = await supabaseAdmin
        .from('settings')
        .select('*')
        .eq('id', existingData.id)
        .single();

      if (retryError || !retrySettings) {
        console.error('Error fetching updated settings after retry:', retryError);
        return null;
      }

      console.log('Updated settings (after retry):', retrySettings);
      return retrySettings;
    }

    console.log('Updated settings:', updatedSettings);
    return updatedSettings;
  } catch (error) {
    console.error('Error in directUpdateSettings:', error);
    return null;
  }
}
